#!/bin/bash

# Test Azure File Share Connection
# Usage: ./test-azure-connection.sh [STORAGE_KEY]

set -e

STORAGE_ACCOUNT="y3dshare"
FILE_SHARE="shared"
TEST_MOUNT="/tmp/azure-test-mount"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
echo_success() { echo -e "${GREEN}✅ $1${NC}"; }
echo_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
echo_error() { echo -e "${RED}❌ $1${NC}"; }

echo_info "🔍 Testing Azure File Share Connection"
echo_info "====================================="

# Get storage key
if [ -n "$1" ]; then
    STORAGE_KEY="$1"
    echo_success "Using provided storage key"
else
    read -s -p "Enter your Azure Storage Account Key: " STORAGE_KEY
    echo ""
    
    if [ -z "$STORAGE_KEY" ]; then
        echo_error "Storage key cannot be empty"
        exit 1
    fi
fi

# Create temporary mount point
echo_info "Creating temporary mount point: $TEST_MOUNT"
mkdir -p "$TEST_MOUNT"

# Create temporary credentials
TEMP_CREDS=$(mktemp)
cat > "$TEMP_CREDS" << EOF
username=$STORAGE_ACCOUNT
password=$STORAGE_KEY
EOF

echo_info "Testing connection to Azure File Share..."

# Test mount
if sudo mount -t cifs \
    "//$STORAGE_ACCOUNT.file.core.windows.net/$FILE_SHARE" \
    "$TEST_MOUNT" \
    -o credentials="$TEMP_CREDS",dir_mode=0755,file_mode=0644,uid=$UID,gid=$GID,iocharset=utf8; then
    
    echo_success "✅ Connection successful!"
    
    # Test write access
    TEST_FILE="$TEST_MOUNT/connection-test-$(date +%s).txt"
    if echo "Connection test at $(date)" > "$TEST_FILE" 2>/dev/null; then
        echo_success "✅ Write access confirmed"
        rm -f "$TEST_FILE"
    else
        echo_warning "⚠️  Read-only access (write failed)"
    fi
    
    # Show some info
    echo_info "📊 File share info:"
    df -h "$TEST_MOUNT" 2>/dev/null || echo "Could not get disk usage"
    
    echo_info "📁 Contents (first 5 items):"
    ls -la "$TEST_MOUNT" 2>/dev/null | head -6 || echo "Could not list contents"
    
    # Cleanup
    sudo umount "$TEST_MOUNT"
    echo_success "✅ Test mount cleaned up"
    
else
    echo_error "❌ Connection failed!"
    echo_info "💡 Possible issues:"
    echo_info "   - Incorrect storage account key"
    echo_info "   - Network connectivity problems"
    echo_info "   - Azure File Share not accessible"
    echo_info "   - CIFS utils not properly installed"
fi

# Cleanup
rm -f "$TEMP_CREDS"
rmdir "$TEST_MOUNT" 2>/dev/null || true

echo_info "🏁 Connection test completed"
