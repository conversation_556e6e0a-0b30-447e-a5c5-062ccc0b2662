#!/bin/bash

# Quick Azure File Share Setup for Y3DHub STL Worker
# Usage: ./quick-setup-azure-mount.sh [STORAGE_KEY]

set -e

# Configuration
STORAGE_ACCOUNT="y3dshare"
FILE_SHARE="shared"
MOUNT_POINT="/mnt/azure-fileshare"
CREDENTIALS_FILE="/etc/azure-fileshare-credentials"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
echo_success() { echo -e "${GREEN}✅ $1${NC}"; }
echo_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
echo_error() { echo -e "${RED}❌ $1${NC}"; }

echo_info "🔧 Quick Azure File Share Setup for Y3DHub STL Worker"
echo_info "======================================================"

# Check if running as root
if [[ $EUID -eq 0 ]]; then
    echo_warning "Running as root. This is fine for setup."
else
    echo_info "Running as user. Will use sudo for system operations."
fi

# Get storage account key
get_storage_key() {
    if [ -n "$1" ]; then
        STORAGE_KEY="$1"
        echo_success "Using provided storage key"
    else
        echo_info "You need your Azure Storage Account Key"
        echo_info "Get it from: Azure Portal > Storage Accounts > y3dshare > Access keys"
        read -s -p "Enter your Azure Storage Account Key: " STORAGE_KEY
        echo ""
        
        if [ -z "$STORAGE_KEY" ]; then
            echo_error "Storage key cannot be empty"
            echo_info "Usage: $0 [STORAGE_KEY]"
            exit 1
        fi
    fi
}

# Create mount point with proper permissions
create_mount_point() {
    echo_info "Creating mount point: $MOUNT_POINT"
    
    if [ ! -d "$MOUNT_POINT" ]; then
        sudo mkdir -p "$MOUNT_POINT"
        echo_success "Mount point created"
    else
        echo_info "Mount point already exists"
    fi
    
    # Set ownership to current user
    sudo chown $USER:$USER "$MOUNT_POINT"
    echo_success "Ownership set to $USER:$USER"
}

# Create credentials file
create_credentials_file() {
    echo_info "Creating credentials file: $CREDENTIALS_FILE"
    
    # Create temporary file with proper permissions
    TEMP_CREDS=$(mktemp)
    cat > "$TEMP_CREDS" << EOF
username=$STORAGE_ACCOUNT
password=$STORAGE_KEY
EOF
    
    # Move to final location with proper permissions
    sudo mv "$TEMP_CREDS" "$CREDENTIALS_FILE"
    sudo chmod 600 "$CREDENTIALS_FILE"
    sudo chown root:root "$CREDENTIALS_FILE"
    
    echo_success "Credentials file created securely"
}

# Mount the file share
mount_fileshare() {
    echo_info "Mounting Azure File Share..."
    
    # Unmount if already mounted
    if mountpoint -q "$MOUNT_POINT" 2>/dev/null; then
        echo_info "Unmounting existing mount..."
        sudo umount "$MOUNT_POINT"
    fi
    
    # Mount the file share
    sudo mount -t cifs \
        "//$STORAGE_ACCOUNT.file.core.windows.net/$FILE_SHARE" \
        "$MOUNT_POINT" \
        -o credentials="$CREDENTIALS_FILE",dir_mode=0755,file_mode=0644,uid=$UID,gid=$GID,iocharset=utf8
    
    if mountpoint -q "$MOUNT_POINT"; then
        echo_success "Azure File Share mounted successfully!"
        echo_info "Available at: $MOUNT_POINT"
    else
        echo_error "Failed to mount Azure File Share"
        exit 1
    fi
}

# Test the mount
test_mount() {
    echo_info "Testing the mount..."
    
    # Test write access
    TEST_FILE="$MOUNT_POINT/test-$(date +%s).txt"
    if echo "Test file created at $(date)" > "$TEST_FILE" 2>/dev/null; then
        echo_success "Write test successful"
        rm -f "$TEST_FILE"
    else
        echo_warning "Write test failed - check permissions"
    fi
    
    # Show mount info
    echo_info "Mount Information:"
    df -h "$MOUNT_POINT" 2>/dev/null || echo "Could not get disk usage info"
}

# Create STL directories
create_stl_directories() {
    echo_info "Creating STL directories..."
    
    mkdir -p "$MOUNT_POINT"/{stl-files,backups,uploads,logs}
    echo_success "Created: stl-files, backups, uploads, logs"
    
    # Create subdirectories for STL organization
    mkdir -p "$MOUNT_POINT/stl-files"/{bubble-style,dual-colours,reg-keys}
    echo_success "Created STL subdirectories"
}

# Add to fstab for persistent mounting
add_to_fstab() {
    echo_info "Adding to /etc/fstab for automatic mounting..."
    
    # Remove existing entry if present
    sudo sed -i "\|$MOUNT_POINT|d" /etc/fstab
    
    # Add new entry
    echo "//$STORAGE_ACCOUNT.file.core.windows.net/$FILE_SHARE $MOUNT_POINT cifs credentials=$CREDENTIALS_FILE,dir_mode=0755,file_mode=0644,uid=$UID,gid=$GID,iocharset=utf8 0 0" | sudo tee -a /etc/fstab > /dev/null
    
    echo_success "Added to /etc/fstab for automatic mounting"
}

# Main execution
main() {
    echo_info "Starting Azure File Share setup..."
    
    # Get storage account key
    get_storage_key "$1"
    
    # Create mount point
    create_mount_point
    
    # Create credentials file
    create_credentials_file
    
    # Mount the file share
    mount_fileshare
    
    # Test the mount
    test_mount
    
    # Create directories
    create_stl_directories
    
    # Add to fstab
    add_to_fstab
    
    echo ""
    echo_success "🎉 Azure File Share setup completed!"
    echo ""
    echo_info "📍 Mount Point: $MOUNT_POINT"
    echo_info "🔗 Azure Path: //$STORAGE_ACCOUNT.file.core.windows.net/$FILE_SHARE"
    echo ""
    echo_info "💡 Next steps:"
    echo_info "   1. Add AZURE_SHARED_DIR=$MOUNT_POINT to your .env file"
    echo_info "   2. Restart your STL worker"
    echo_info "   3. Test STL file generation"
    echo ""
    echo_info "🔧 Useful commands:"
    echo_info "   - Check mount: mountpoint $MOUNT_POINT"
    echo_info "   - Unmount: sudo umount $MOUNT_POINT"
    echo_info "   - Remount: sudo mount -a"
    echo_info "   - View space: df -h $MOUNT_POINT"
}

# Run main function
main "$1"
