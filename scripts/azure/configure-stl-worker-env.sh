#!/bin/bash

# Configure STL Worker Environment Variables for Azure Integration
# This script adds the necessary environment variables to .env files

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
echo_success() { echo -e "${GREEN}✅ $1${NC}"; }
echo_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
echo_error() { echo -e "${RED}❌ $1${NC}"; }

MOUNT_POINT="/mnt/azure-fileshare"

echo_info "🔧 Configuring STL Worker Environment Variables"
echo_info "=============================================="

# Function to add or update environment variable in .env file
update_env_var() {
    local env_file="$1"
    local var_name="$2"
    local var_value="$3"
    
    if [ ! -f "$env_file" ]; then
        echo_warning ".env file not found: $env_file"
        return 1
    fi
    
    # Check if variable already exists
    if grep -q "^${var_name}=" "$env_file"; then
        # Update existing variable
        sed -i "s|^${var_name}=.*|${var_name}=\"${var_value}\"|" "$env_file"
        echo_info "Updated $var_name in $env_file"
    else
        # Add new variable
        echo "" >> "$env_file"
        echo "# Azure File Share Configuration" >> "$env_file"
        echo "${var_name}=\"${var_value}\"" >> "$env_file"
        echo_info "Added $var_name to $env_file"
    fi
}

# Function to configure environment for a directory
configure_env() {
    local dir="$1"
    local env_file="$dir/.env"
    
    echo_info "Configuring environment for: $dir"
    
    if [ ! -f "$env_file" ]; then
        echo_warning "No .env file found in $dir"
        return 1
    fi
    
    # Add Azure configuration
    update_env_var "$env_file" "AZURE_SHARED_DIR" "$MOUNT_POINT"
    
    # Add STL worker specific configurations
    update_env_var "$env_file" "STL_OUTPUT_DIR" "./output_stl"
    update_env_var "$env_file" "OPENSCAD_DIR" "./openscad"
    
    # Add worker concurrency if not set
    if ! grep -q "^STL_WORKER_CONCURRENCY=" "$env_file"; then
        update_env_var "$env_file" "STL_WORKER_CONCURRENCY" "2"
    fi
    
    echo_success "Environment configured for $dir"
}

# Main execution
main() {
    echo_info "Starting environment configuration..."
    
    # Check if Azure mount exists
    if ! mountpoint -q "$MOUNT_POINT" 2>/dev/null; then
        echo_error "Azure File Share not mounted at $MOUNT_POINT"
        echo_info "Please run the Azure setup script first:"
        echo_info "  ./scripts/azure/quick-setup-azure-mount.sh [STORAGE_KEY]"
        exit 1
    fi
    
    echo_success "Azure File Share is mounted at $MOUNT_POINT"
    
    # Configure current directory
    configure_env "."
    
    # Look for other Y3DHub directories (production, staging)
    for dir in ../Y3DHub_production ../Y3DHub_main; do
        if [ -d "$dir" ]; then
            echo_info "Found additional Y3DHub directory: $dir"
            configure_env "$dir"
        fi
    done
    
    echo ""
    echo_success "🎉 Environment configuration completed!"
    echo ""
    echo_info "📋 Configuration Summary:"
    echo_info "   - AZURE_SHARED_DIR: $MOUNT_POINT"
    echo_info "   - STL_OUTPUT_DIR: ./output_stl"
    echo_info "   - OPENSCAD_DIR: ./openscad"
    echo_info "   - STL_WORKER_CONCURRENCY: 2"
    echo ""
    echo_info "💡 Next steps:"
    echo_info "   1. Restart your STL worker processes"
    echo_info "   2. Test STL file generation"
    echo_info "   3. Check Azure File Share for uploaded files"
    echo ""
    echo_info "🔍 To verify configuration:"
    echo_info "   grep AZURE .env"
    echo_info "   ls -la $MOUNT_POINT"
}

# Run main function
main
