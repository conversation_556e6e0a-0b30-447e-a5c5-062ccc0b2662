#!/bin/bash

# Verify STL Worker Azure Setup
# This script checks that everything is properly configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
echo_success() { echo -e "${GREEN}✅ $1${NC}"; }
echo_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
echo_error() { echo -e "${RED}❌ $1${NC}"; }

MOUNT_POINT="/mnt/azure-fileshare"
CREDENTIALS_FILE="/etc/azure-fileshare-credentials"

echo_info "🔍 Verifying STL Worker Azure Setup"
echo_info "==================================="

# Check Azure mount
check_azure_mount() {
    echo_info "Checking Azure File Share mount..."
    
    if mountpoint -q "$MOUNT_POINT" 2>/dev/null; then
        echo_success "Azure File Share is mounted at $MOUNT_POINT"
        
        # Check permissions
        if [ -w "$MOUNT_POINT" ]; then
            echo_success "Write permissions OK"
        else
            echo_error "No write permissions to $MOUNT_POINT"
            return 1
        fi
        
        # Show disk usage
        echo_info "Disk usage:"
        df -h "$MOUNT_POINT" 2>/dev/null || echo_warning "Could not get disk usage"
        
    else
        echo_error "Azure File Share not mounted at $MOUNT_POINT"
        return 1
    fi
}

# Check credentials file
check_credentials() {
    echo_info "Checking credentials file..."
    
    if [ -f "$CREDENTIALS_FILE" ]; then
        echo_success "Credentials file exists: $CREDENTIALS_FILE"
        
        # Check permissions
        local perms=$(stat -c "%a" "$CREDENTIALS_FILE")
        if [ "$perms" = "600" ]; then
            echo_success "Credentials file permissions OK (600)"
        else
            echo_warning "Credentials file permissions: $perms (should be 600)"
        fi
    else
        echo_error "Credentials file not found: $CREDENTIALS_FILE"
        return 1
    fi
}

# Check environment variables
check_environment() {
    echo_info "Checking environment variables..."
    
    if [ -f ".env" ]; then
        echo_success ".env file found"
        
        # Check for Azure configuration
        if grep -q "AZURE_SHARED_DIR" .env; then
            local azure_dir=$(grep "AZURE_SHARED_DIR" .env | cut -d'=' -f2 | tr -d '"')
            echo_success "AZURE_SHARED_DIR configured: $azure_dir"
            
            if [ "$azure_dir" = "$MOUNT_POINT" ]; then
                echo_success "AZURE_SHARED_DIR points to correct mount point"
            else
                echo_warning "AZURE_SHARED_DIR ($azure_dir) doesn't match mount point ($MOUNT_POINT)"
            fi
        else
            echo_error "AZURE_SHARED_DIR not configured in .env"
            return 1
        fi
        
        # Check other STL worker variables
        for var in STL_OUTPUT_DIR OPENSCAD_DIR; do
            if grep -q "$var" .env; then
                local value=$(grep "$var" .env | cut -d'=' -f2 | tr -d '"')
                echo_success "$var configured: $value"
            else
                echo_warning "$var not configured in .env"
            fi
        done
        
    else
        echo_error ".env file not found"
        return 1
    fi
}

# Check directories
check_directories() {
    echo_info "Checking directories..."
    
    # Check local STL output directory
    if [ -d "output_stl" ]; then
        echo_success "Local STL output directory exists: output_stl"
        echo_info "Contents:"
        ls -la output_stl/ | head -5
    else
        echo_warning "Local STL output directory not found: output_stl"
    fi
    
    # Check OpenSCAD directory
    if [ -d "openscad" ]; then
        echo_success "OpenSCAD directory exists: openscad"
    else
        echo_warning "OpenSCAD directory not found: openscad"
    fi
    
    # Check Azure directories
    if [ -d "$MOUNT_POINT/stl-files" ]; then
        echo_success "Azure STL directory exists: $MOUNT_POINT/stl-files"
    else
        echo_warning "Azure STL directory not found: $MOUNT_POINT/stl-files"
    fi
}

# Test file operations
test_file_operations() {
    echo_info "Testing file operations..."
    
    local test_file="$MOUNT_POINT/test-stl-worker-$(date +%s).txt"
    
    # Test write
    if echo "STL Worker test file created at $(date)" > "$test_file" 2>/dev/null; then
        echo_success "Write test successful"
        
        # Test read
        if cat "$test_file" >/dev/null 2>&1; then
            echo_success "Read test successful"
            
            # Clean up
            rm -f "$test_file"
            echo_success "File cleanup successful"
        else
            echo_error "Read test failed"
            return 1
        fi
    else
        echo_error "Write test failed"
        return 1
    fi
}

# Check STL worker processes
check_processes() {
    echo_info "Checking STL worker processes..."
    
    # Check for Node.js STL worker
    if pgrep -f "stl-render-worker" >/dev/null; then
        echo_success "STL render worker process is running"
    else
        echo_warning "STL render worker process not found"
    fi
    
    # Check for Python STL worker
    if pgrep -f "stlworker" >/dev/null; then
        echo_success "Python STL worker process is running"
    else
        echo_warning "Python STL worker process not found"
    fi
}

# Main execution
main() {
    echo_info "Starting verification..."
    echo ""
    
    local errors=0
    
    # Run all checks
    check_azure_mount || ((errors++))
    echo ""
    
    check_credentials || ((errors++))
    echo ""
    
    check_environment || ((errors++))
    echo ""
    
    check_directories || ((errors++))
    echo ""
    
    test_file_operations || ((errors++))
    echo ""
    
    check_processes
    echo ""
    
    # Summary
    if [ $errors -eq 0 ]; then
        echo_success "🎉 All checks passed! STL Worker Azure setup is complete."
        echo ""
        echo_info "📋 Setup Summary:"
        echo_info "   ✅ Azure File Share mounted and accessible"
        echo_info "   ✅ Credentials configured securely"
        echo_info "   ✅ Environment variables set"
        echo_info "   ✅ Directories exist and accessible"
        echo_info "   ✅ File operations working"
        echo ""
        echo_info "🚀 Your STL worker is ready to use Azure File Share!"
    else
        echo_error "❌ $errors check(s) failed. Please review the issues above."
        echo ""
        echo_info "💡 Common fixes:"
        echo_info "   - Run: ./scripts/azure/quick-setup-azure-mount.sh [STORAGE_KEY]"
        echo_info "   - Run: ./scripts/azure/configure-stl-worker-env.sh"
        echo_info "   - Check Azure storage account key"
        echo_info "   - Verify network connectivity to Azure"
        exit 1
    fi
}

# Run main function
main
