#!/bin/bash

# Azure File Share Setup Script for WSL/Linux
# This script will mount the Azure File Share to your local system

set -e

# Configuration
STORAGE_ACCOUNT="y3dshare"
FILE_SHARE="shared"
MOUNT_POINT="/mnt/azure-fileshare"
CREDENTIALS_FILE="/etc/azure-fileshare-credentials"

echo "🔧 Azure File Share Setup for Y3DHub"
echo "======================================"
echo ""
echo "Storage Account: $STORAGE_ACCOUNT"
echo "File Share: $FILE_SHARE"
echo "Mount Point: $MOUNT_POINT"
echo ""

# Check if running as root for some operations
if [[ $EUID -eq 0 ]]; then
    echo "⚠️  Running as root. This is fine for setup."
else
    echo "ℹ️  Running as user. Will use sudo for system operations."
fi

# Function to get storage account key
get_storage_key() {
    echo ""
    echo "🔑 Azure Storage Account Key Required"
    echo "======================================"
    echo ""
    echo "To get your storage account key:"
    echo "1. Go to Azure Portal"
    echo "2. Navigate to Storage Accounts > y3dshare"
    echo "3. Go to Security + networking > Access keys"
    echo "4. Copy key1 or key2"
    echo ""
    # Check if storage key is provided as argument
    if [ -n "$1" ]; then
        STORAGE_KEY="$1"
        echo "✅ Using provided storage key"
    else
        read -s -p "Enter your Azure Storage Account Key: " STORAGE_KEY
        echo ""

        if [ -z "$STORAGE_KEY" ]; then
            echo "❌ Storage key cannot be empty"
            echo "💡 Usage: $0 [STORAGE_KEY]"
            exit 1
        fi
    fi
}

# Function to create mount point
create_mount_point() {
    echo "📁 Creating mount point: $MOUNT_POINT"
    sudo mkdir -p "$MOUNT_POINT"
    sudo chown $USER:$USER "$MOUNT_POINT"
    echo "✅ Mount point created"
}

# Function to create credentials file
create_credentials_file() {
    echo "🔐 Creating credentials file: $CREDENTIALS_FILE"
    
    # Create temporary file with proper permissions
    TEMP_CREDS=$(mktemp)
    cat > "$TEMP_CREDS" << EOF
username=$STORAGE_ACCOUNT
password=$STORAGE_KEY
EOF
    
    # Move to final location with proper permissions
    sudo mv "$TEMP_CREDS" "$CREDENTIALS_FILE"
    sudo chmod 600 "$CREDENTIALS_FILE"
    sudo chown root:root "$CREDENTIALS_FILE"
    
    echo "✅ Credentials file created securely"
}

# Function to mount the file share
mount_fileshare() {
    echo "🔗 Mounting Azure File Share..."
    
    # Unmount if already mounted
    if mountpoint -q "$MOUNT_POINT"; then
        echo "📤 Unmounting existing mount..."
        sudo umount "$MOUNT_POINT"
    fi
    
    # Mount the file share
    sudo mount -t cifs \
        "//$STORAGE_ACCOUNT.file.core.windows.net/$FILE_SHARE" \
        "$MOUNT_POINT" \
        -o credentials="$CREDENTIALS_FILE",dir_mode=0755,file_mode=0644,uid=$UID,gid=$GID,iocharset=utf8
    
    if mountpoint -q "$MOUNT_POINT"; then
        echo "✅ Azure File Share mounted successfully!"
        echo "📁 Available at: $MOUNT_POINT"
    else
        echo "❌ Failed to mount Azure File Share"
        exit 1
    fi
}

# Function to add to fstab for persistent mounting
add_to_fstab() {
    echo ""
    read -p "🔄 Add to /etc/fstab for automatic mounting on boot? (y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "📝 Adding to /etc/fstab..."
        
        # Remove existing entry if present
        sudo sed -i "\|$MOUNT_POINT|d" /etc/fstab
        
        # Add new entry
        echo "//$STORAGE_ACCOUNT.file.core.windows.net/$FILE_SHARE $MOUNT_POINT cifs credentials=$CREDENTIALS_FILE,dir_mode=0755,file_mode=0644,uid=$UID,gid=$GID,iocharset=utf8 0 0" | sudo tee -a /etc/fstab > /dev/null
        
        echo "✅ Added to /etc/fstab for automatic mounting"
    fi
}

# Function to test the mount
test_mount() {
    echo ""
    echo "🧪 Testing the mount..."
    
    # Test write access
    TEST_FILE="$MOUNT_POINT/test-$(date +%s).txt"
    if echo "Test file created at $(date)" > "$TEST_FILE" 2>/dev/null; then
        echo "✅ Write test successful"
        rm -f "$TEST_FILE"
    else
        echo "⚠️  Write test failed - check permissions"
    fi
    
    # Show mount info
    echo ""
    echo "📊 Mount Information:"
    df -h "$MOUNT_POINT" 2>/dev/null || echo "Could not get disk usage info"
    echo ""
    echo "📂 Contents:"
    ls -la "$MOUNT_POINT" 2>/dev/null || echo "Could not list contents"
}

# Function to create useful directories
create_directories() {
    echo ""
    read -p "📁 Create standard directories (backups, uploads, shared)? (y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "📁 Creating standard directories..."
        mkdir -p "$MOUNT_POINT"/{backups,uploads,shared,logs}
        echo "✅ Created: backups, uploads, shared, logs"
    fi
}

# Main execution
main() {
    echo "Starting Azure File Share setup..."
    echo ""
    
    # Get storage account key
    get_storage_key
    
    # Create mount point
    create_mount_point
    
    # Create credentials file
    create_credentials_file
    
    # Mount the file share
    mount_fileshare
    
    # Test the mount
    test_mount
    
    # Create directories
    create_directories
    
    # Add to fstab
    add_to_fstab
    
    echo ""
    echo "🎉 Azure File Share setup completed!"
    echo ""
    echo "📍 Mount Point: $MOUNT_POINT"
    echo "🔗 Azure Path: //$STORAGE_ACCOUNT.file.core.windows.net/$FILE_SHARE"
    echo ""
    echo "💡 Useful commands:"
    echo "   - Check mount: mountpoint $MOUNT_POINT"
    echo "   - Unmount: sudo umount $MOUNT_POINT"
    echo "   - Remount: sudo mount -a"
    echo "   - View space: df -h $MOUNT_POINT"
    echo ""
}

# Run main function
main
