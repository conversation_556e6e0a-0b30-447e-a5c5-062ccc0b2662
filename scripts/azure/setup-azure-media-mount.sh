#!/bin/bash

# Setup Azure File Share at /media/shared
# Based on your provided commands

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
echo_success() { echo -e "${GREEN}✅ $1${NC}"; }
echo_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
echo_error() { echo -e "${RED}❌ $1${NC}"; }

echo_info "🔧 Setting up Azure File Share at /media/shared"
echo_info "=============================================="

# Step 1: Create mount directory
echo_info "Creating mount directory..."
sudo mkdir -p /media/shared
echo_success "Created /media/shared"

# Step 2: Create credentials directory
echo_info "Creating credentials directory..."
if [ ! -d "/etc/smbcredentials" ]; then
    sudo mkdir /etc/smbcredentials
    echo_success "Created /etc/smbcredentials"
else
    echo_info "/etc/smbcredentials already exists"
fi

# Step 3: Create credentials file
echo_info "Creating credentials file..."
if [ ! -f "/etc/smbcredentials/y3dshare.cred" ]; then
    sudo bash -c 'echo "username=y3dshare" >> /etc/smbcredentials/y3dshare.cred'
    sudo bash -c 'echo "password=5qHGu2bnwZOwrBijpK34SAYKcDOgX2lsGc20MN++FD8/dymc3NxuBq+C167o/ay/TemukOfOR9MR+ASt8HQouQ==" >> /etc/smbcredentials/y3dshare.cred'
    echo_success "Created credentials file"
else
    echo_info "Credentials file already exists"
fi

# Step 4: Set proper permissions
echo_info "Setting credentials file permissions..."
sudo chmod 600 /etc/smbcredentials/y3dshare.cred
echo_success "Set permissions to 600"

# Step 5: Add to fstab (check if not already present)
echo_info "Adding to /etc/fstab..."
FSTAB_ENTRY="//y3dshare.file.core.windows.net/shared /media/shared cifs nofail,credentials=/etc/smbcredentials/y3dshare.cred,dir_mode=0755,file_mode=0755,serverino,nosharesock,mfsymlinks,actimeo=30"

if ! grep -q "/media/shared" /etc/fstab; then
    sudo bash -c "echo '$FSTAB_ENTRY' >> /etc/fstab"
    echo_success "Added to /etc/fstab"
else
    echo_info "Entry already exists in /etc/fstab"
fi

# Step 6: Mount the file share
echo_info "Mounting Azure File Share..."
if mountpoint -q /media/shared 2>/dev/null; then
    echo_info "Already mounted, unmounting first..."
    sudo umount /media/shared
fi

sudo mount -t cifs //y3dshare.file.core.windows.net/shared /media/shared -o credentials=/etc/smbcredentials/y3dshare.cred,dir_mode=0755,file_mode=0755,serverino,nosharesock,mfsymlinks,actimeo=30

if mountpoint -q /media/shared; then
    echo_success "✅ Azure File Share mounted successfully at /media/shared"
else
    echo_error "❌ Failed to mount Azure File Share"
    exit 1
fi

# Step 7: Set proper ownership
echo_info "Setting ownership to jayson:jayson..."
sudo chown jayson:jayson /media/shared
echo_success "Ownership set"

# Step 8: Test access
echo_info "Testing access..."
TEST_FILE="/media/shared/test-$(date +%s).txt"
if echo "Test file created at $(date)" > "$TEST_FILE" 2>/dev/null; then
    echo_success "✅ Write test successful"
    rm -f "$TEST_FILE"
else
    echo_warning "⚠️  Write test failed - check permissions"
fi

# Step 9: Create STL directories
echo_info "Creating STL directories..."
mkdir -p /media/shared/{stl-files,backups,uploads,logs}
mkdir -p /media/shared/stl-files/{bubble-style,dual-colours,reg-keys}
echo_success "Created directory structure"

# Step 10: Show status
echo ""
echo_success "🎉 Azure File Share setup completed!"
echo ""
echo_info "📍 Mount Point: /media/shared"
echo_info "🔗 Azure Path: //y3dshare.file.core.windows.net/shared"
echo ""
echo_info "📊 Mount Status:"
df -h /media/shared 2>/dev/null || echo "Could not get disk usage"
echo ""
echo_info "📁 Directory Structure:"
ls -la /media/shared/ 2>/dev/null || echo "Could not list contents"
echo ""
echo_info "💡 Next step: Configure environment variables"
echo_info "   Run: ./scripts/azure/configure-stl-worker-env-media.sh"
