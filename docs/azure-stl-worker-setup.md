# Azure STL Worker Setup Guide

This guide will help you set up Azure File Share integration for the Y3DHub STL worker with proper directory permissions.

## Overview

The STL worker can optionally copy generated STL files to an Azure File Share for backup and sharing purposes. This setup ensures:

- ✅ Proper directory permissions for the `jayson` user
- ✅ Secure Azure File Share mounting
- ✅ Automatic file synchronization
- ✅ Persistent mount configuration

## Prerequisites

1. **Azure Storage Account**: `y3dshare`
2. **File Share**: `shared`
3. **Storage Account Key**: Available from Azure Portal
4. **CIFS Utils**: Already installed (`mount.cifs` available)

## Quick Setup

### Step 1: Mount Azure File Share

Run the quick setup script with your storage account key:

```bash
# Replace YOUR_STORAGE_ACCOUNT_KEY with your actual key
./scripts/azure/quick-setup-azure-mount.sh YOUR_STORAGE_ACCOUNT_KEY

# Or run interactively (will prompt for key)
./scripts/azure/quick-setup-azure-mount.sh
```

This script will:
- Create mount point `/mnt/azure-fileshare`
- Set proper ownership (`jayson:jayson`)
- Create secure credentials file
- Mount the Azure File Share
- Create necessary directories
- Add to `/etc/fstab` for persistence

### Step 2: Configure Environment Variables

```bash
./scripts/azure/configure-stl-worker-env.sh
```

This adds the following to your `.env` file:
```env
# Azure File Share Configuration
AZURE_SHARED_DIR="/mnt/azure-fileshare"
STL_OUTPUT_DIR="./output_stl"
OPENSCAD_DIR="./openscad"
STL_WORKER_CONCURRENCY="2"
```

### Step 3: Verify Setup

```bash
./scripts/azure/verify-stl-worker-setup.sh
```

This checks:
- Azure mount status and permissions
- Credentials file security
- Environment variables
- Directory structure
- File operations
- Running processes

## Manual Setup (Alternative)

If you prefer manual setup or need to troubleshoot:

### 1. Install CIFS Utils (if needed)
```bash
sudo apt update && sudo apt install -y cifs-utils
```

### 2. Create Mount Point
```bash
sudo mkdir -p /mnt/azure-fileshare
sudo chown jayson:jayson /mnt/azure-fileshare
```

### 3. Create Credentials File
```bash
sudo tee /etc/azure-fileshare-credentials > /dev/null << EOF
username=y3dshare
password=YOUR_STORAGE_ACCOUNT_KEY
EOF

sudo chmod 600 /etc/azure-fileshare-credentials
sudo chown root:root /etc/azure-fileshare-credentials
```

### 4. Mount File Share
```bash
sudo mount -t cifs \
  //y3dshare.file.core.windows.net/shared \
  /mnt/azure-fileshare \
  -o credentials=/etc/azure-fileshare-credentials,dir_mode=0755,file_mode=0644,uid=1000,gid=1000,iocharset=utf8
```

### 5. Add to fstab
```bash
echo "//y3dshare.file.core.windows.net/shared /mnt/azure-fileshare cifs credentials=/etc/azure-fileshare-credentials,dir_mode=0755,file_mode=0644,uid=1000,gid=1000,iocharset=utf8 0 0" | sudo tee -a /etc/fstab
```

## Directory Structure

After setup, you'll have:

```
/mnt/azure-fileshare/
├── stl-files/           # STL files copied here
│   ├── bubble-style/
│   ├── dual-colours/
│   └── reg-keys/
├── backups/             # Database/config backups
├── uploads/             # Manual file uploads
└── logs/                # Log files
```

## How It Works

1. **STL Generation**: Worker generates STL files locally in `./output_stl/`
2. **Azure Copy**: If `AZURE_SHARED_DIR` is set, files are copied to Azure
3. **Directory Structure**: Maintains same folder structure on Azure
4. **Error Handling**: Continues even if Azure copy fails

## Troubleshooting

### Mount Issues
```bash
# Check if mounted
mountpoint /mnt/azure-fileshare

# Check mount details
mount | grep azure-fileshare

# Remount
sudo umount /mnt/azure-fileshare
sudo mount -a
```

### Permission Issues
```bash
# Check ownership
ls -la /mnt/azure-fileshare

# Fix ownership
sudo chown jayson:jayson /mnt/azure-fileshare
```

### Network Issues
```bash
# Test connectivity
ping y3dshare.file.core.windows.net

# Check credentials
sudo cat /etc/azure-fileshare-credentials
```

### Environment Issues
```bash
# Check environment variables
grep AZURE .env

# Restart STL worker
pm2 restart stl-worker
```

## Security Notes

- ✅ Credentials stored in `/etc/azure-fileshare-credentials` with 600 permissions
- ✅ Only root can read credentials file
- ✅ Mount uses specific UID/GID for security
- ✅ File permissions set to 644 (read-only for others)

## Monitoring

Check Azure File Share usage:
```bash
df -h /mnt/azure-fileshare
ls -la /mnt/azure-fileshare/stl-files/
```

Monitor STL worker logs:
```bash
tail -f logs/*.log
pm2 logs stl-worker
```

## Next Steps

1. ✅ Run the setup scripts
2. ✅ Verify everything works
3. ✅ Test STL generation
4. ✅ Check files appear in Azure
5. ✅ Monitor for any issues

The STL worker will now automatically copy generated files to Azure File Share while maintaining the local workflow.
