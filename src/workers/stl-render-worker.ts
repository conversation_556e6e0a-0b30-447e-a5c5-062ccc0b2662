import { createReadStream } from 'fs';
import fs from 'fs/promises';
import path from 'path';

import {
  Customer,
  OrderItem,
  PrintOrderTask,
  PrintOrderTask_status,
  PrintOrderTask_stl_render_state,
  Prisma,
  Product,
} from '@prisma/client'; // Removed unused PrismaClient
import { google } from 'googleapis';
import slug from 'slug'; // Added import for slug

import { renderScadToStl } from '../lib/openscad/index';
import { prisma } from '../lib/prisma'; // CORRECTED Import Prisma client

// Only import what is used

// Interface for the render settings file structure
interface RenderSettings {
  parameterSets: Record<string, Record<string, string | number | boolean | Array<string | number>>>;
  fileFormatVersion: string;
}

let loadedRenderSettings: RenderSettings | null = null;

// Helper function to automatically distribute text across up to three lines
function autoDistributeText(text: string): { line1: string; line2: string; line3: string } {
  if (!text) return { line1: '', line2: '', line3: '' };

  const words = text
    .trim()
    .split(/\s+/)
    .filter(w => w.length > 0);
  const numWords = words.length;

  let line1 = '',
    line2 = '',
    line3 = '';

  if (numWords === 0) {
    return { line1, line2, line3 };
  }
  if (numWords === 1) {
    line1 = words[0];
    return { line1, line2, line3 };
  }

  // Tunable parameters for line length
  const idealCharsPerLine = 12; // Target for good balance
  const maxCharsPerLineAbsolute = 20; // Harder limit for a line before forcing a new one

  const linesResult: string[] = [];
  let currentLineAgg = '';

  for (let i = 0; i < numWords; i++) {
    const word = words[i];
    if (currentLineAgg === '') {
      currentLineAgg = word;
    } else {
      const potentialLength = currentLineAgg.length + 1 + word.length;
      // Condition to add to current line:
      // 1. Potential length does not exceed absolute max
      // AND (
      //   2a. Potential length is within ideal
      //   OR
      //   2b. We are already building the third line (linesResult.length === 2)
      // )
      if (
        potentialLength <= maxCharsPerLineAbsolute &&
        (potentialLength <= idealCharsPerLine || linesResult.length === 2)
      ) {
        currentLineAgg += ' ' + word;
      } else {
        // Time to move to a new line or current line is too long
        linesResult.push(currentLineAgg);
        currentLineAgg = word; // Start new line with current word

        // If we've just completed the second line, all remaining words go on the third line
        if (linesResult.length === 2) {
          for (let j = i + 1; j < numWords; j++) {
            currentLineAgg += ' ' + words[j];
          }
          i = numWords; // End the outer loop as all words are placed
        }
      }
    }
  }
  linesResult.push(currentLineAgg); // Add the last line being built

  // Distribute to line1, line2, line3
  line1 = linesResult[0] || '';
  line2 = linesResult[1] || '';
  line3 = linesResult[2] || '';

  // Specific override for 3 words to prefer "FirstName MiddleName", "LastName"
  if (numWords === 3) {
    const firstTwoCombined = words[0] + ' ' + words[1];
    const thirdWord = words[2];
    // If this specific structure is valid (lengths are okay)
    if (
      firstTwoCombined.length <= maxCharsPerLineAbsolute &&
      thirdWord.length <= idealCharsPerLine
    ) {
      // And if the default distribution used 3 lines, or this 2-line version is notably more balanced
      const defaultL1 = linesResult[0] || '';
      const defaultL2 = linesResult[1] || '';
      const defaultL3 = linesResult[2] || '';

      if (
        defaultL3 !== '' || // Default used 3 lines
        (defaultL2 !== '' &&
          Math.abs(defaultL1.length - defaultL2.length) >
            Math.abs(firstTwoCombined.length - thirdWord.length) + 2) // Default used 2 lines, but this is more balanced by a margin
      ) {
        line1 = firstTwoCombined;
        line2 = thirdWord;
        line3 = '';
      }
    }
  }

  // Specific override for 4 words to prefer "FirstName MiddleName", "OtherName LastName"
  if (numWords === 4) {
    const firstTwoCombined = words[0] + ' ' + words[1];
    const lastTwoCombined = words[2] + ' ' + words[3];
    // If this specific structure is valid
    if (
      firstTwoCombined.length <= maxCharsPerLineAbsolute &&
      lastTwoCombined.length <= maxCharsPerLineAbsolute
    ) {
      const defaultL3 = linesResult[2] || '';
      // Prefer this 2-line version if the default used 3 lines
      if (defaultL3 !== '') {
        line1 = firstTwoCombined;
        line2 = lastTwoCombined;
        line3 = '';
      }
    }
  }
  return { line1, line2, line3 };
}

// Specialized auto-distribution function for Y3D-NKC-002 Bubble style
// More aggressive about splitting text into multiple lines for better bubble appearance
function autoDistributeTextBubble(text: string): { line1: string; line2: string; line3: string } {
  if (!text) return { line1: '', line2: '', line3: '' };

  const words = text
    .trim()
    .split(/\s+/)
    .filter(w => w.length > 0);
  const numWords = words.length;

  let line1 = '',
    line2 = '',
    line3 = '';

  if (numWords === 0) {
    return { line1, line2, line3 };
  }
  if (numWords === 1) {
    line1 = words[0];
    return { line1, line2, line3 };
  }

  // More aggressive parameters for bubble style - prefer shorter lines
  const idealCharsPerLine = 8; // Shorter target for bubble style
  const maxCharsPerLineAbsolute = 15; // Lower hard limit for bubble style

  const linesResult: string[] = [];
  let currentLineAgg = '';

  for (let i = 0; i < numWords; i++) {
    const word = words[i];
    if (currentLineAgg === '') {
      currentLineAgg = word;
    } else {
      const potentialLength = currentLineAgg.length + 1 + word.length;
      // More aggressive splitting - prefer to split even at ideal length
      if (
        potentialLength <= maxCharsPerLineAbsolute &&
        (potentialLength <= idealCharsPerLine || linesResult.length === 2)
      ) {
        currentLineAgg += ' ' + word;
      } else {
        // Time to move to a new line
        linesResult.push(currentLineAgg);
        currentLineAgg = word; // Start new line with current word

        // If we've just completed the second line, all remaining words go on the third line
        if (linesResult.length === 2) {
          for (let j = i + 1; j < numWords; j++) {
            currentLineAgg += ' ' + words[j];
          }
          i = numWords; // End the outer loop as all words are placed
        }
      }
    }
  }
  linesResult.push(currentLineAgg); // Add the last line being built

  // Distribute to line1, line2, line3
  line1 = linesResult[0] || '';
  line2 = linesResult[1] || '';
  line3 = linesResult[2] || '';

  // For 2 words, always split them (bubble style preference)
  if (numWords === 2) {
    line1 = words[0];
    line2 = words[1];
    line3 = '';
  }

  // For 3 words, prefer "Word1", "Word2 Word3" if lengths are reasonable
  if (numWords === 3) {
    const firstWord = words[0];
    const lastTwoCombined = words[1] + ' ' + words[2];
    if (
      firstWord.length <= maxCharsPerLineAbsolute &&
      lastTwoCombined.length <= maxCharsPerLineAbsolute
    ) {
      line1 = firstWord;
      line2 = lastTwoCombined;
      line3 = '';
    }
  }

  return { line1, line2, line3 };
}

// Removed unused RENDER_STATE, STL_OUTPUT_DIR_ABS, STL_OUTPUT_DIR_RELATIVE, GDRIVE_RETRY_DELAY_MS and ExecError.
// The helper functions getProductFolder and getAlphaFolder are implemented later in this file.
// resetStuckTasks was removed.

// Configuration --------------------------
const MAX_RETRIES = parseInt(process.env.STL_RENDER_MAX_RETRIES || '3', 10);
const CONCURRENCY = Number(process.env.STL_WORKER_CONCURRENCY ?? '2');
const POLL_INTERVAL_MS = Number(process.env.STL_WORKER_POLL_MS ?? '5000');
// New flag to control skipping existing files (defaults to false - overwrite by default)
const SKIP_IF_EXISTS = process.argv.includes('--skip-if-exists');

// Google Drive integration config
const GDRIVE_ENABLED = process.env.GDRIVE_ENABLED === 'true';
const GDRIVE_FOLDER_ID = process.env.GDRIVE_FOLDER_ID || ''; // Folder ID where STL files will be uploaded
const GDRIVE_SERVICE_ACCOUNT_PATH =
  process.env.GDRIVE_SERVICE_ACCOUNT_PATH || './service-account.json';

// Helper Functions -----------------------

/**
 * Uploads a file to Google Drive using service account authentication.
 * Ensures directory structure matching localRelativePath is created under GDRIVE_FOLDER_ID.
 * Overwrites existing files with the same name in the target Drive folder.
 * @param localFilePath The absolute local path to the file to upload.
 * @param localRelativePath The relative path of the file (e.g., product-folder/alpha-folder/filename.stl) used to create Drive folders.
 * @param fileName The name of the file to be created/updated in Google Drive.
 * @returns The file info if successful, null otherwise.
 */
async function uploadToGoogleDrive(
  localFilePath: string,
  localRelativePath: string,
  fileName: string
): Promise<{ id: string; webViewLink: string; webContentLink: string } | null> {
  if (!GDRIVE_ENABLED || !GDRIVE_FOLDER_ID) {
    console.log(
      `[${new Date().toISOString()}] Google Drive upload skipped: not enabled or missing GDRIVE_FOLDER_ID.`
    );
    return null;
  }
  if (!localRelativePath) {
    console.warn(
      `[${new Date().toISOString()}] Google Drive upload skipped: localRelativePath is empty for ${fileName}. File will be uploaded to root Drive folder.`
    );
    // Fallback to root if relative path is somehow not provided, though this shouldn't happen with current logic.
  }

  try {
    const auth = new google.auth.GoogleAuth({
      keyFile: GDRIVE_SERVICE_ACCOUNT_PATH,
      scopes: ['https://www.googleapis.com/auth/drive.file'],
    });
    const drive = google.drive({ version: 'v3', auth });

    let currentParentFolderId = GDRIVE_FOLDER_ID;
    const pathSegments = path
      .dirname(localRelativePath)
      .split(path.sep)
      .filter(segment => segment.length > 0 && segment !== '.');

    for (const segment of pathSegments) {
      currentParentFolderId = await findOrCreateDriveFolder(drive, segment, currentParentFolderId);
    }

    // Check if file already exists in the target folder to update it
    const cleanFileName = fileName.replace(/'/g, "\\'");
    const existingFileQuery = `name='${cleanFileName}' and '${currentParentFolderId}' in parents and trashed=false and mimeType!='application/vnd.google-apps.folder'`;
    const searchRes = await drive.files.list({
      q: existingFileQuery,
      fields: 'files(id)',
      spaces: 'drive',
      pageSize: 1,
    });

    const media = {
      mimeType: 'application/sla', // STL MIME type, ensure this is correct for your files
      body: createReadStream(localFilePath),
    };

    let fileId = null;
    let action = 'Uploaded new';

    if (searchRes.data.files && searchRes.data.files.length > 0 && searchRes.data.files[0].id) {
      fileId = searchRes.data.files[0].id;
      // File exists, update it
      const updateRes = await drive.files.update({
        fileId: fileId,
        media: media,
        // requestBody: { name: fileName }, // To update metadata like name if needed
        fields: 'id, webViewLink, webContentLink',
      });
      action = 'Updated existing';
      console.log(
        `[${new Date().toISOString()}] ${action} STL in Google Drive: ${localRelativePath} (ID: ${updateRes.data.id})`
      );
      return updateRes.data as { id: string; webViewLink: string; webContentLink: string };
    } else {
      // File does not exist, create it
      const fileMetadata = {
        name: fileName,
        parents: [currentParentFolderId],
      };
      const createRes = await drive.files.create({
        requestBody: fileMetadata,
        media: media,
        fields: 'id, webViewLink, webContentLink',
      });
      console.log(
        `[${new Date().toISOString()}] ${action} STL to Google Drive: ${localRelativePath} (ID: ${createRes.data.id})`
      );
      return createRes.data as { id: string; webViewLink: string; webContentLink: string };
    }
  } catch (err) {
    console.error(
      `[${new Date().toISOString()}] Error uploading to Google Drive (${localRelativePath}):`,
      err
    );
    return null;
  }
}

/**
 * Finds an existing folder by name within a parent folder in Google Drive, or creates it if not found.
 * @param drive The authenticated Google Drive API client.
 * @param folderName The name of the folder to find or create.
 * @param parentFolderId The ID of the parent folder in Google Drive.
 * @returns The ID of the found or created folder.
 */
async function findOrCreateDriveFolder(
  drive: ReturnType<typeof google.drive>,
  folderName: string,
  parentFolderId: string
): Promise<string> {
  // Clean folder name to avoid issues with Drive API query syntax if it contains single quotes
  const cleanFolderName = folderName.replace(/'/g, "\\'");
  const query = `mimeType='application/vnd.google-apps.folder' and name='${cleanFolderName}' and '${parentFolderId}' in parents and trashed=false`;
  let folderId = '';

  try {
    const searchRes = await drive.files.list({
      q: query,
      fields: 'files(id)',
      spaces: 'drive',
      pageSize: 1, // We only need one if it exists
    });

    if (searchRes.data.files && searchRes.data.files.length > 0 && searchRes.data.files[0].id) {
      folderId = searchRes.data.files[0].id;
      console.log(
        `[${new Date().toISOString()}] Found Drive folder: ${folderName} (ID: ${folderId}) in parent ${parentFolderId}`
      );
    } else {
      const folderMetadata = {
        name: folderName,
        mimeType: 'application/vnd.google-apps.folder',
        parents: [parentFolderId],
      };
      const createRes = await drive.files.create({
        requestBody: folderMetadata,
        fields: 'id',
      });
      if (!createRes.data.id) {
        throw new Error(`Failed to create folder '${folderName}' - no ID returned.`);
      }
      folderId = createRes.data.id;
      console.log(
        `[${new Date().toISOString()}] Created Drive folder: ${folderName} (ID: ${folderId}) in parent ${parentFolderId}`
      );
    }
    return folderId;
  } catch (error) {
    console.error(
      `[${new Date().toISOString()}] Error finding/creating Drive folder '${folderName}' in parent '${parentFolderId}':`,
      error
    );
    // Propagate the error to be handled by the calling function (uploadToGoogleDrive)
    // This ensures the main operation might be retried or logged as failed appropriately.
    throw error;
  }
}

// Database Interaction -------------------
async function reserveTask(): Promise<TaskWithProduct | null> {
  let taskToProcess: TaskWithProduct | null = null;

  try {
    await prisma.$transaction(
      async tx => {
        const potentialTasks = await tx.printOrderTask.findMany({
          where: {
            stl_render_state: PrintOrderTask_stl_render_state.pending,
            render_retries: { lt: MAX_RETRIES },
          },
          orderBy: [{ ship_by_date: 'asc' }, { created_at: 'desc' }],
          take: 10,
          include: {
            OrderItem: { include: { Product: true } },
            Customer: true,
          },
        });

        if (potentialTasks.length === 0) {
          return;
        }

        let reservedBaseTask:
          | (PrintOrderTask & {
              OrderItem: (OrderItem & { Product: Product }) | null;
              Customer: Customer | null;
            })
          | null = null;

        for (const task of potentialTasks) {
          if (!task.OrderItem || !task.OrderItem.Product) {
            console.warn(
              `[${new Date().toISOString()}] Task ${task.id} skipped: missing OrderItem or Product data. Marking as failed.`
            );
            await tx.printOrderTask.update({
              where: { id: task.id },
              data: {
                stl_render_state: PrintOrderTask_stl_render_state.failed,
                annotation:
                  'Task data integrity issue: Missing OrderItem or Product. Cannot process.',
                render_retries: MAX_RETRIES,
              },
            });
            continue;
          }

          const result = await tx.printOrderTask.updateMany({
            where: {
              id: task.id,
              stl_render_state: PrintOrderTask_stl_render_state.pending,
            },
            data: {
              stl_render_state: PrintOrderTask_stl_render_state.running,
              render_retries: { increment: 1 },
              updated_at: new Date(),
            },
          });

          if (result.count > 0) {
            // Fetch the full task again to ensure we have the latest state and all included relations
            // This specific task variable will have the correct types for relations.
            const freshlyReservedTask = await tx.printOrderTask.findUnique({
              where: { id: task.id },
              include: {
                OrderItem: { include: { Product: true } },
                Customer: true,
              },
            });

            if (
              freshlyReservedTask &&
              freshlyReservedTask.OrderItem &&
              freshlyReservedTask.OrderItem.Product
            ) {
              reservedBaseTask = {
                ...freshlyReservedTask,
                OrderItem: freshlyReservedTask.OrderItem as OrderItem & { Product: Product },
                Customer: freshlyReservedTask.Customer,
              } as PrintOrderTask & {
                OrderItem: OrderItem & { Product: Product };
                Customer: Customer | null;
              };
              break;
            } else {
              // Should not happen if previous checks passed and update was successful
              console.error(
                `[${new Date().toISOString()}] Failed to re-fetch task ${task.id} with relations after reserving.`
              );
              // Potentially revert the state or handle error
            }
          }
        }

        if (reservedBaseTask && reservedBaseTask.OrderItem && reservedBaseTask.OrderItem.Product) {
          const productForTask = reservedBaseTask.OrderItem.Product;

          let openscadModelPath: string | null = null;
          let openscadParameters: Prisma.JsonValue = {};

          // Determine the OpenSCAD model and parameters based on the product SKU
          if (productForTask.sku?.startsWith('PER-KEY3D')) {
            openscadModelPath = 'DualColour.scad';
            const customText = reservedBaseTask.custom_text || '';
            let lines = { line1: '', line2: '', line3: '' };

            if (customText.match(/\r?\n|\\|\//)) {
              // Explicit delimiters found, use existing logic
              const textLines = customText.split(/\r?\n|\\|\//).map(t => t.trim());
              lines.line1 = textLines[0] || productForTask.name || 'DefaultText';
              lines.line2 = textLines[1] || '';
              lines.line3 = textLines[2] || '';
            } else {
              // No explicit delimiters, use new auto-distribution logic
              lines = autoDistributeText(customText);
              // If customText was empty or only whitespace, autoDistributeText returns all empty.
              // In that case, use product name as default for line1.
              if (!lines.line1 && !lines.line2 && !lines.line3) {
                lines.line1 = productForTask.name || 'DefaultText';
              }
            }

            openscadParameters = {
              line1: lines.line1,
              line2: lines.line2,
              line3: lines.line3,
              // Add font spacing parameters if needed
              font_narrow_widen: -5, // Default to -5 to make text slightly narrower
              character_spacing: 0.95, // Default to 0.95 to bring characters closer together
            };
            console.log(
              `[${new Date().toISOString()}] DEBUG - OpenSCAD parameters:`,
              JSON.stringify(openscadParameters, null, 2)
            );
          } else if (productForTask.sku?.startsWith('REG-KEY')) {
            openscadModelPath = 'registration_keys/reg_key_v2.scad';
            openscadParameters = {
              reg_number: reservedBaseTask.custom_text || 'ABC123XYZ',
            };
            console.log(
              `[${new Date().toISOString()}] Task ${reservedBaseTask.id} (${productForTask.sku}): Using reg_key_v2.scad with reg_number='${reservedBaseTask.custom_text || ''}'`
            );
          } else if (productForTask.sku?.startsWith('PER-2PER-')) {
            // Cable clip needs special handling (generates two files - 3.5mm and 4.0mm versions)
            openscadModelPath = 'cable_clips/cable_clip.scad';
            // Only use first line of custom text for cable clips
            const clipText =
              (reservedBaseTask.custom_text || '')
                .split(/\r?\n|\\|\//)
                .map(line => line.trim())
                .filter(line => line.length > 0)[0] ||
              productForTask.name ||
              'DefaultText';

            openscadParameters = {
              text: clipText,
              size: 3.5, // Default to 3.5mm for the primary file, 4.0mm will be handled in processTask
            };
            console.log(
              `[${new Date().toISOString()}] Task ${reservedBaseTask.id} (${productForTask.sku}): Using cable_clip.scad with text='${clipText}'`
            );
          } else if (
            productForTask.sku === 'Y3D-NKC-002' ||
            (productForTask.sku && productForTask.sku.startsWith('Y3D-NKC-002-'))
          ) {
            // Bubble style keychain - uses DualColour.scad with Bubble parameter set from Master.json
            openscadModelPath = 'DualColour.scad';

            const customText = reservedBaseTask.custom_text || productForTask.name || 'DefaultText';
            let lines: { line1: string; line2: string; line3: string };

            if (customText.match(/\r?\n|\\|\//)) {
              // Explicit delimiters found, use existing logic
              const textLines = customText.split(/\r?\n|\\|\//).map(t => t.trim());
              lines = {
                line1: textLines[0] || productForTask.name || 'DefaultText',
                line2: textLines[1] || '',
                line3: textLines[2] || '',
              };
            } else {
              // No explicit delimiters, use bubble-specific auto-distribution logic
              lines = autoDistributeTextBubble(customText);
              // If customText was empty or only whitespace, autoDistributeTextBubble returns all empty.
              // In that case, use product name as default for line1.
              if (!lines.line1 && !lines.line2 && !lines.line3) {
                lines.line1 = productForTask.name || 'DefaultText';
              }
            }

            openscadParameters = {
              line1: lines.line1,
              line2: lines.line2,
              line3: lines.line3,
              // Special flag to indicate we want to use the Bubble configuration from Master.json
              _useConfigSet: 'Bubble',
            };
            console.log(
              `[${new Date().toISOString()}] Task ${reservedBaseTask.id} (${productForTask.sku}): Using DualColour.scad with Bubble config for lines='${lines.line1}' / '${lines.line2}' / '${lines.line3}'`
            );
          } else if (
            productForTask.sku === 'Y3D-NKC-001' ||
            (productForTask.sku && productForTask.sku.startsWith('Y3D-NKC-001-'))
          ) {
            // Signature style keychain - uses DualColour.scad with Signature parameter set from Master.json
            openscadModelPath = 'DualColour.scad';

            const customText = reservedBaseTask.custom_text || productForTask.name || 'DefaultText';

            // Signature style typically uses single line text (like a signature)
            // If there are line breaks, take only the first line
            const signatureText =
              customText.split(/\r?\n|\\|\//).map(t => t.trim())[0] ||
              productForTask.name ||
              'DefaultText';

            openscadParameters = {
              line1: signatureText,
              line2: '',
              line3: '',
              // Special flag to indicate we want to use the Signature configuration from Master.json
              _useConfigSet: 'Signature',
            };
            console.log(
              `[${new Date().toISOString()}] Task ${reservedBaseTask.id} (${productForTask.sku}): Using DualColour.scad with Signature config for text='${signatureText}'`
            );
          } else if (productForTask.sku === 'Y3D-REGKEY-STL1') {
            // Registration key style - uses RegKey.scad with Text parameter
            openscadModelPath = 'RegKey.scad';

            const customText = reservedBaseTask.custom_text || productForTask.name || 'ABC 123';

            // RegKey.scad expects a single "Text" parameter
            openscadParameters = {
              Text: customText,
            };

            console.log(
              `[${new Date().toISOString()}] Task ${reservedBaseTask.id} (${productForTask.sku}): Using RegKey.scad with Text='${customText}'`
            );
          } else {
            console.warn(
              `[${new Date().toISOString()}] Task ${reservedBaseTask.id} for SKU ${productForTask.sku}: Unknown product type for OpenSCAD model/param mapping.`
            );
            // openscadModelPath remains null
            // openscadParameters remains {}
          }

          taskToProcess = {
            ...reservedBaseTask,
            // Ensure OrderItem and Customer are correctly typed as per TaskWithProduct
            OrderItem: reservedBaseTask.OrderItem as OrderItem & { Product: Product },
            Customer: reservedBaseTask.Customer, // Already Customer | null
            resolvedProductInfo: {
              sku: productForTask.sku || 'unknown_sku',
              name: productForTask.name || 'Unknown Product',
              openscad_model_path: openscadModelPath,
              openscad_parameters: openscadParameters,
            },
          };
        }
      },
      {
        maxWait: 10000,
        timeout: 20000,
      }
    );

    if (taskToProcess) {
      console.log(
        `[${new Date().toISOString()}] Reserved task ${(taskToProcess as TaskWithProduct).id}. Attempt #${(taskToProcess as TaskWithProduct).render_retries}.`
      );
    }
    return taskToProcess as TaskWithProduct | null;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error in reserveTask:`, error);
    // If a task was partially reserved but an error occurred before returning,
    // it might be stuck in 'running'. The resetStuckTasks job should eventually clean it up.
    return null;
  }
}

// Added markTaskAsFailed function
async function markTaskAsFailed(
  tx: Prisma.TransactionClient,
  taskId: string,
  errorMessage: string,
  // failureAttemptNumber is task.render_retries from processTask
  // (i.e., the number of the attempt that just failed)
  failureAttemptNumber: number
): Promise<void> {
  // If this failureAttemptNumber meets or exceeds MAX_RETRIES, it's permanently failed.
  const failureState =
    failureAttemptNumber >= MAX_RETRIES
      ? PrintOrderTask_stl_render_state.failed
      : PrintOrderTask_stl_render_state.pending;

  await tx.printOrderTask.update({
    where: { id: taskId },
    data: {
      stl_render_state: failureState,
      // render_retries is already correctly set by reserveTask for this attempt.
      // No need to increment it here.
      annotation: `Failure on attempt ${failureAttemptNumber}: ${errorMessage.substring(0, 1000)}`,
      updated_at: new Date(),
    },
  });
  const retryMessage =
    failureState === PrintOrderTask_stl_render_state.pending
      ? `Will retry (next attempt will be ${failureAttemptNumber + 1} of ${MAX_RETRIES}).`
      : `Permanently failed after ${failureAttemptNumber} attempts.`;
  console.error(
    `[${new Date().toISOString()}] Task ${taskId} processing failed. Marked as ${failureState}. ${retryMessage} Error: ${errorMessage.substring(0, 200)}`
  );
}

// Type for task records used by processTask.
// It extends the base Prisma PrintOrderTask and includes resolved/derived data needed for rendering.
interface TaskWithProduct extends PrintOrderTask {
  // Extends the base Prisma type
  // Ensure related data fetched by Prisma is correctly typed here
  OrderItem: OrderItem & {
    Product: Product;
  };
  Customer: Customer | null; // Customer can be null

  // Resolved product information specifically for rendering logic,
  // including derived OpenSCAD paths and parameters.
  resolvedProductInfo: {
    sku: string;
    name: string;
    openscad_model_path: string | null;
    openscad_parameters: Prisma.JsonValue;
  };
}

// Add a retry wrapper for reserveTask
async function reserveTaskWithRetry(maxRetries = 5): Promise<TaskWithProduct | null> {
  let attempt = 0;
  while (attempt < maxRetries) {
    try {
      return await reserveTask();
    } catch (err: unknown) {
      if (
        typeof err === 'object' &&
        err !== null &&
        'code' in err &&
        (err as { code?: string }).code === 'P2034'
      ) {
        // Deadlock/write conflict, retry after a short delay
        await new Promise(res => setTimeout(res, 100 + Math.random() * 200));
        attempt++;
        continue;
      }
      throw err; // Other errors: rethrow
    }
  }
  throw new Error('Failed to reserve task after multiple retries due to deadlocks.');
}

// Worker Logic --------------------------
async function processTask(task: TaskWithProduct, isTestRun: boolean = false): Promise<void> {
  const taskId = task.id;
  const productSku = task.resolvedProductInfo.sku;
  const openscadModel = task.resolvedProductInfo.openscad_model_path;
  const openscadParameters = task.resolvedProductInfo.openscad_parameters;
  const customText = task.custom_text || '';

  if (!productSku || !openscadModel) {
    await prisma.$transaction(async tx => {
      // Pass task.render_retries, which is the current attempt number that failed.
      await markTaskAsFailed(
        tx,
        taskId,
        `Essential data missing: SKU (${productSku}), Model (${openscadModel})`,
        task.render_retries
      );
    });
    console.error(
      `[${new Date().toISOString()}] Task ${taskId} failed: SKU or OpenSCAD model path missing.`
    );
    return;
  }

  const openscadDir = path.resolve(process.env.OPENSCAD_DIR || './openscad');
  const outputBaseDir = path.resolve(process.env.STL_OUTPUT_DIR || './output_stl');

  // Azure shared folder for STL files (set via AZURE_SHARED_DIR env var)
  const azureSharedDir = process.env.AZURE_SHARED_DIR || '';
  const useAzure = azureSharedDir !== '';

  // Old logic: product folder and alpha folder
  const productFolder = getProductFolder(productSku);
  // Use first line of customText for alpha folder and filename
  const mainText =
    customText
      .split(/\r?\n|\\|\//)
      .map(t => t.trim())
      .filter(Boolean)[0] || `Tag${taskId}`;
  const alphaFolder = getAlphaFolder(slug(mainText));
  const absDir = path.join(outputBaseDir, productFolder, alphaFolder);
  await fs.mkdir(absDir, { recursive: true });

  // Filename: safe slug of mainText, add _UC if all uppercase
  const safeName = slug(mainText);
  const isAllUpper = safeName === safeName.toUpperCase() && /[A-Z]/.test(safeName);
  const baseOutputFilename = `${safeName}${isAllUpper ? '_UC' : ''}.stl`;
  const stlAbsolutePath = path.join(absDir, baseOutputFilename);
  const relativeStlPath = path.join(productFolder, alphaFolder, baseOutputFilename);

  // Check if STL file already exists and if we should skip rendering
  if (SKIP_IF_EXISTS) {
    try {
      await fs.access(stlAbsolutePath);
      console.log(
        `[${new Date().toISOString()}] Task ${taskId}: STL file ${stlAbsolutePath} already exists. Skipping render.`
      );
      await prisma.printOrderTask.update({
        where: { id: taskId },
        data: {
          stl_path: relativeStlPath,
          stl_render_state: PrintOrderTask_stl_render_state.completed,
          annotation: 'Render skipped: STL file already existed.',
          updated_at: new Date(),
        },
      });
      return; // Successfully skipped
    } catch (_e) {
      // File does not exist, proceed to render
    }
  }

  // --- OpenSCAD Rendering ---
  const modelFullPath = path.join(openscadDir, openscadModel);
  console.log(
    `[${new Date().toISOString()}] Rendering STL for task ${taskId} to ${stlAbsolutePath}`
  );
  await execOpenSCAD(modelFullPath, stlAbsolutePath, openscadParameters);
  console.log(
    `[${new Date().toISOString()}] STL rendering complete for task ${taskId}. Output: ${stlAbsolutePath}`
  );

  // Copy STL file to Azure shared folder maintaining directory structure, if configured
  if (useAzure) {
    try {
      const azureProductDir = path.join(azureSharedDir, productFolder);
      const azureAlphaDir = path.join(azureProductDir, alphaFolder);
      await fs.mkdir(azureAlphaDir, { recursive: true });
      const azureStlPath = path.join(azureAlphaDir, baseOutputFilename);
      await fs.copyFile(stlAbsolutePath, azureStlPath);
      console.log(
        `[${new Date().toISOString()}] STL file copied to Azure shared folder: ${azureStlPath}`
      );
    } catch (azureError) {
      console.warn(
        `[${new Date().toISOString()}] Failed to copy STL to Azure shared folder: ${azureError instanceof Error ? azureError.message : String(azureError)}`
      );
      // Continue even if Azure copy fails
    }
  } else {
    console.log(`[${new Date().toISOString()}] AZURE_SHARED_DIR not set; skipping Azure copy.`);
  }

  // Prepare paths for upload - default to just one file
  const stlPathsForUpload = [
    {
      localPath: stlAbsolutePath,
      driveFilename: baseOutputFilename,
      relativePath: relativeStlPath,
    },
  ];

  // Special case for cable clips: Generate a second file with 4.0mm size
  if (productSku.startsWith('PER-2PER-')) {
    // Create a second filename for the 4.0mm version
    const baseOutputFilename40 = `${safeName}${isAllUpper ? '_UC' : ''}_40mm.stl`;
    const stlAbsolutePath40 = path.join(absDir, baseOutputFilename40);
    const relativeStlPath40 = path.join(productFolder, alphaFolder, baseOutputFilename40);

    // Create parameters for 4.0mm size
    // Convert the openscadParameters to a record to avoid the spread type error
    const params: Record<string, string | number> = {};
    if (
      typeof openscadParameters === 'object' &&
      openscadParameters !== null &&
      !Array.isArray(openscadParameters)
    ) {
      Object.entries(openscadParameters).forEach(([key, value]) => {
        if (typeof value === 'string' || typeof value === 'number') {
          params[key] = value;
        }
      });
    }

    // Set the size to 4.0mm
    params.size = 4.0;

    console.log(
      `[${new Date().toISOString()}] Rendering secondary STL (4.0mm) for cable clip task ${taskId} to ${stlAbsolutePath40}`
    );
    await execOpenSCAD(modelFullPath, stlAbsolutePath40, params);
    console.log(
      `[${new Date().toISOString()}] Secondary STL (4.0mm) rendering complete for task ${taskId}. Output: ${stlAbsolutePath40}`
    );

    // Copy secondary STL file to Azure shared folder maintaining directory structure
    try {
      const azureProductDir = path.join(azureSharedDir, productFolder);
      const azureAlphaDir = path.join(azureProductDir, alphaFolder);
      await fs.mkdir(azureAlphaDir, { recursive: true });
      const azureStlPath40 = path.join(azureAlphaDir, baseOutputFilename40);
      await fs.copyFile(stlAbsolutePath40, azureStlPath40);
      console.log(
        `[${new Date().toISOString()}] Secondary STL file copied to Azure shared folder: ${azureStlPath40}`
      );
    } catch (azureError) {
      console.warn(
        `[${new Date().toISOString()}] Failed to copy secondary STL to Azure shared folder: ${azureError instanceof Error ? azureError.message : String(azureError)}`
      );
    }

    // Add the second file to the upload list
    stlPathsForUpload.push({
      localPath: stlAbsolutePath40,
      driveFilename: baseOutputFilename40,
      relativePath: relativeStlPath40,
    });
  }

  let driveFileInfo: { id: string; webViewLink: string } | null = null;

  // --- Google Drive Upload (if enabled) ---
  if (GDRIVE_ENABLED && !isTestRun) {
    console.log(`[${new Date().toISOString()}] Task ${taskId}: Uploading to Google Drive...`);
    if (!GDRIVE_FOLDER_ID) {
      throw new Error('GDRIVE_FOLDER_ID is not set in environment variables.');
    }
    // Initialize Google Drive client
    const auth = new google.auth.GoogleAuth({
      keyFile: GDRIVE_SERVICE_ACCOUNT_PATH,
      scopes: ['https://www.googleapis.com/auth/drive.file'],
    });
    const drive = google.drive({ version: 'v3', auth });
    // Prepare the path segments for folder creation
    const pathSegments = [productFolder, alphaFolder];
    // Start from root folder and create/navigate the path
    let currentFolderId = GDRIVE_FOLDER_ID;
    for (const segment of pathSegments) {
      currentFolderId = await findOrCreateDriveFolder(drive, segment, currentFolderId);
    }
    // Upload the file(s)
    const uploadResults = await Promise.all(
      stlPathsForUpload.map(async fileToUpload => {
        return uploadToGoogleDrive(
          fileToUpload.localPath,
          fileToUpload.relativePath,
          fileToUpload.driveFilename
        );
      })
    );
    driveFileInfo = uploadResults[0]; // Assuming single file upload for now
    if (driveFileInfo && driveFileInfo.id) {
      console.log(
        `[${new Date().toISOString()}] Task ${taskId}: Successfully uploaded to Google Drive. File ID: ${driveFileInfo.id}`
      );
    } else {
      console.warn(
        `[${new Date().toISOString()}] Task ${taskId}: Google Drive upload completed but no file ID returned. Check logs.`
      );
    }
  }

  // Transaction to update task status and save file paths
  if (!isTestRun) {
    await prisma.$transaction(async tx => {
      await tx.printOrderTask.update({
        where: { id: taskId },
        data: {
          stl_render_state: PrintOrderTask_stl_render_state.completed,
          stl_path: relativeStlPath,
          annotation: `Rendered and saved to ${relativeStlPath}. ${GDRIVE_ENABLED && driveFileInfo ? 'Uploaded to GDrive.' : ''}`,
          gdrive_file_id: driveFileInfo?.id ?? task.gdrive_file_id,
          gdrive_public_link: driveFileInfo?.webViewLink ?? task.gdrive_public_link,
          updated_at: new Date(),
        },
      });
      console.log(
        `[${new Date().toISOString()}] Task ${taskId} completed successfully. STL at: ${relativeStlPath}`
      );
    });
  } else {
    console.log(
      `[${new Date().toISOString()}] [TEST RUN] Task ${taskId} processing complete. STL should be at: ${relativeStlPath}`
    );
    console.log(
      `[${new Date().toISOString()}] [TEST RUN] Skipping database update and Google Drive upload.`
    );
  }
}

// Helper to fix invalid stl_render_state values in the DB
async function fixInvalidRenderStates(): Promise<number> {
  try {
    // Find and update records whose state is not one of the known valid states,
    // but ONLY if they haven't already been successfully rendered (i.e., stl_path is not set).
    // This prevents resetting completed tasks even if their state was somehow corrupted later.
    const validStates: PrintOrderTask_stl_render_state[] = [
      PrintOrderTask_stl_render_state.pending,
      PrintOrderTask_stl_render_state.running,
      PrintOrderTask_stl_render_state.completed,
      PrintOrderTask_stl_render_state.failed,
    ];
    const count = await prisma.$executeRaw`
            UPDATE PrintOrderTask
            SET stl_render_state = ${PrintOrderTask_stl_render_state.pending}
            WHERE stl_render_state NOT IN (${Prisma.join(validStates.map(s => s.toString()))})
              AND (stl_path IS NULL OR stl_path = '')
        `;

    if (typeof count === 'number' && count > 0) {
      console.log(
        `[${new Date().toISOString()}] Corrected ${count} tasks with invalid stl_render_state to '${PrintOrderTask_stl_render_state.pending}'.`
      );
    }
    return typeof count === 'number' ? count : 0;
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error fixing invalid render states:`, error);
    return 0;
  }
}

// Individual worker loop logic
async function workerLoop(workerId: number): Promise<void> {
  console.log(`[${new Date().toISOString()}] Worker ${workerId} starting.`);
  // eslint-disable-next-line no-constant-condition
  while (true) {
    let task: TaskWithProduct | null = null;
    try {
      task = await reserveTaskWithRetry();

      if (task) {
        await processTask(task);
      } else {
        // No task found, wait before polling again
        await new Promise(resolve => setTimeout(resolve, POLL_INTERVAL_MS));
      }
    } catch (error: unknown) {
      // This catch block handles errors from reserveTask or if processTask re-throws an error
      // (which it should, to roll back its transaction)
      const currentAttemptNumber = task ? task.render_retries : 1; // task.render_retries is the current attempt that failed if task is defined
      let baseErrorMessage = `Unhandled error in worker ${workerId}`;
      if (error instanceof Error) baseErrorMessage = error.message;
      else baseErrorMessage = String(error);

      console.error(
        `[${new Date().toISOString()}] Worker ${workerId} unhandled error for task ${task?.id ?? 'UNKNOWN'} (attempt ${currentAttemptNumber}):`,
        baseErrorMessage,
        error instanceof Error ? error.stack : ''
      );

      // Capture task details for safe access in nested catch
      const taskForErrorReporting = task;

      if (taskForErrorReporting && taskForErrorReporting.id) {
        const taskIdForDbErrorLogging = taskForErrorReporting.id; // For use in the nested catch
        try {
          await prisma.$transaction(async tx => {
            // taskForErrorReporting.render_retries is the number of the attempt that just failed.
            await markTaskAsFailed(
              tx,
              taskForErrorReporting.id,
              `Unhandled worker error: ${baseErrorMessage}`,
              taskForErrorReporting.render_retries
            );
          });
        } catch (dbError) {
          console.error(
            `[${new Date().toISOString()}] CRITICAL: Worker ${workerId} failed to mark task ${taskIdForDbErrorLogging} as failed in fallback DB error handler:`,
            dbError
          );
        }
      }
      // Wait a bit before trying to reserve another task to prevent rapid error loops
      await new Promise(resolve => setTimeout(resolve, POLL_INTERVAL_MS * 2));
    }
  }
}

// Main function to initialize and run workers
async function main(): Promise<void> {
  console.log(
    `[${new Date().toISOString()}] STL Render Worker starting with ${CONCURRENCY} concurrent workers. Polling every ${POLL_INTERVAL_MS}ms.`
  );
  console.log(
    `[${new Date().toISOString()}] SKIP_IF_EXISTS is ${SKIP_IF_EXISTS ? 'ENABLED' : 'DISABLED'}.`
  );

  // Check for command-line options
  const manualTaskFlag = process.argv.find(arg => arg.startsWith('--task='));
  const refreshFlag = process.argv.includes('--refresh');
  const clearGDriveFlag = process.argv.includes('--clear-gdrive');
  const testTextDistributionFlag = process.argv.find(arg => arg.startsWith('--test-text='));
  const testSkuFlag = process.argv.find(arg => arg.startsWith('--test-sku='));

  if (testTextDistributionFlag) {
    const textToTest = testTextDistributionFlag.split('=')[1];
    if (!textToTest) {
      console.error(
        `[${new Date().toISOString()}] Invalid text for testing. Usage: --test-text="Your text here" [--test-sku=SKU]`
      );
      process.exit(1);
    }

    // Get the SKU to test (default to PER-KEY3D-STY3-Y3D for backward compatibility)
    const testSku = testSkuFlag ? testSkuFlag.split('=')[1] : 'PER-KEY3D-STY3-Y3D';
    const supportedSkus = [
      'PER-KEY3D-STY3-Y3D',
      'Y3D-NKC-002',
      'Y3D-NKC-001',
      'Y3D-REGKEY-STL1',
      'REG-KEY',
      'PER-2PER-TEST',
    ];

    if (!supportedSkus.some(sku => testSku.startsWith(sku.replace('-TEST', '')))) {
      console.error(
        `[${new Date().toISOString()}] Unsupported test SKU: ${testSku}. Supported: ${supportedSkus.join(', ')}`
      );
      process.exit(1);
    }

    console.log(`[${new Date().toISOString()}] Testing SKU: ${testSku} with text: "${textToTest}"`);

    // For multi-line SKUs, show text distribution
    if (testSku.startsWith('PER-KEY3D')) {
      const { line1, line2, line3 } = autoDistributeText(textToTest);
      console.log(`  Line 1: "${line1}"`);
      console.log(`  Line 2: "${line2}"`);
      console.log(`  Line 3: "${line3}"`);
    } else if (testSku === 'Y3D-NKC-002' || testSku.startsWith('Y3D-NKC-002-')) {
      const { line1, line2, line3 } = autoDistributeTextBubble(textToTest);
      console.log(`  Line 1: "${line1}"`);
      console.log(`  Line 2: "${line2}"`);
      console.log(`  Line 3: "${line3}"`);
    } else if (testSku === 'Y3D-NKC-001' || testSku.startsWith('Y3D-NKC-001-')) {
      // Signature style - single line only
      const signatureText = textToTest.split(/\r?\n|\\|\//).map(t => t.trim())[0] || textToTest;
      console.log(`  Signature Text: "${signatureText}"`);
    } else if (testSku === 'Y3D-REGKEY-STL1') {
      // Registration key style - single text parameter
      console.log(`  Registration Key Text: "${textToTest}"`);
    }

    console.log(`[${new Date().toISOString()}] [TEST RUN] Proceeding to generate STL for test.`);
    await initializeRenderSettings(); // Ensure settings are loaded

    // Construct a dummy TaskWithProduct for testing
    const testProductName = `Test ${testSku} Product`;

    // Determine OpenSCAD model and parameters based on SKU
    let openscadModelPath: string;
    let openscadParameters: Prisma.JsonValue = {};

    if (testSku.startsWith('PER-KEY3D')) {
      const { line1, line2, line3 } = autoDistributeText(textToTest);
      openscadModelPath = 'DualColour.scad';
      openscadParameters = {
        line1: line1,
        line2: line2,
        line3: line3,
        font_narrow_widen: -5,
        character_spacing: 0.95,
      };
    } else if (testSku === 'Y3D-NKC-002') {
      openscadModelPath = 'DualColour.scad';

      // Use auto-distribution logic like other multi-line SKUs
      let lines: { line1: string; line2: string; line3: string };
      if (textToTest.match(/\r?\n|\\|\//)) {
        // Explicit delimiters found, use existing logic
        const textLines = textToTest.split(/\r?\n|\\|\//).map(t => t.trim());
        lines = {
          line1: textLines[0] || 'DefaultText',
          line2: textLines[1] || '',
          line3: textLines[2] || '',
        };
      } else {
        // No explicit delimiters, use bubble-specific auto-distribution logic
        lines = autoDistributeTextBubble(textToTest);
        if (!lines.line1 && !lines.line2 && !lines.line3) {
          lines.line1 = 'DefaultText';
        }
      }

      openscadParameters = {
        line1: lines.line1,
        line2: lines.line2,
        line3: lines.line3,
        _useConfigSet: 'Bubble',
      };
    } else if (testSku === 'Y3D-NKC-001' || testSku.startsWith('Y3D-NKC-001-')) {
      openscadModelPath = 'DualColour.scad';

      // Signature style - single line only
      const signatureText = textToTest.split(/\r?\n|\\|\//).map(t => t.trim())[0] || textToTest;

      openscadParameters = {
        line1: signatureText,
        line2: '',
        line3: '',
        _useConfigSet: 'Signature',
      };
    } else if (testSku === 'Y3D-REGKEY-STL1') {
      openscadModelPath = 'RegKey.scad';
      openscadParameters = {
        Text: textToTest,
      };
    } else if (testSku.startsWith('REG-KEY')) {
      openscadModelPath = 'registration_keys/reg_key_v2.scad';
      openscadParameters = {
        reg_number: textToTest,
      };
    } else if (testSku.startsWith('PER-2PER-')) {
      openscadModelPath = 'cable_clips/cable_clip.scad';
      openscadParameters = {
        text: textToTest,
        size: 3.5,
      };
    } else {
      console.error(`[${new Date().toISOString()}] Unsupported test SKU: ${testSku}`);
      process.exit(1);
    }

    const dummyTask: TaskWithProduct = {
      id: `test-text-${Date.now()}`,
      orderId: 999999,
      marketplace_order_number: 'test-order',
      customerId: null,
      orderItemId: 999999,
      taskIndex: 0,
      productId: 999999,
      shorthandProductName: testProductName,
      quantity: 1,
      color_1: null,
      color_2: null,
      status: PrintOrderTask_status.pending,
      needs_review: false,
      review_reason: null,
      isVerified: false,
      verifiedAt: null,
      verifiedByUserId: null,
      custom_text: textToTest,
      stl_path: null,
      render_retries: 0,
      stl_render_state: PrintOrderTask_stl_render_state.pending,
      annotation: '[TEST RUN]',
      created_at: new Date(),
      updated_at: new Date(),
      ship_by_date: new Date(),
      gdrive_file_id: null,
      gdrive_public_link: null,
      OrderItem: {
        id: 999999,
        orderId: 999999,
        productId: 999999,
        quantity: 1,
        unit_price: new Prisma.Decimal(0),
        print_settings: null,
        created_at: new Date(),
        updated_at: new Date(),
        shipstationLineItemKey: null,
        Product: {
          id: 999999,
          sku: testSku,
          name: testProductName,
          imageUrl: null,
          weight: null,
          notes: 'Test Product for STL generation',
          createdAt: new Date(),
          updatedAt: new Date(),
          fulfillment_sku: null,
          item_weight_units: null,
          item_weight_value: null,
          shipstation_product_id: null,
          upc: null,
          warehouse_location: null,
        },
      },
      Customer: null,
      resolvedProductInfo: {
        sku: testSku,
        name: testProductName,
        openscad_model_path: openscadModelPath,
        openscad_parameters: openscadParameters,
      },
    };

    try {
      await processTask(dummyTask, true);
      console.log(`[${new Date().toISOString()}] [TEST RUN] STL generation process finished.`);
    } catch (error) {
      console.error(`[${new Date().toISOString()}] [TEST RUN] Error during STL generation:`, error);
      process.exit(1);
    }

    process.exit(0); // Exit after testing
  }

  if (manualTaskFlag) {
    const taskId = manualTaskFlag.split('=')[1];
    if (!taskId) {
      console.error(`[${new Date().toISOString()}] Invalid task ID. Usage: --task=<taskId>`);
      process.exit(1);
    }
    console.log(`[${new Date().toISOString()}] Manual mode: Processing single task ${taskId}`);
    await processManualTask(taskId);
    return; // Exit after processing manual task
  }

  if (refreshFlag) {
    console.log(
      `[${new Date().toISOString()}] Refresh mode: Resetting eligible tasks to pending state`
    );
    await resetTasksToPending();
    return; // Exit after resetting tasks
  }

  if (clearGDriveFlag) {
    if (!GDRIVE_ENABLED) {
      console.error(
        `[${new Date().toISOString()}] Google Drive is not enabled. Cannot clear files.`
      );
      process.exit(1);
    }
    console.log(
      `[${new Date().toISOString()}] Clearing all files in Google Drive folder: ${GDRIVE_FOLDER_ID}`
    );
    await clearGDriveFiles();
    return; // Exit after clearing files
  }

  if (GDRIVE_ENABLED) {
    console.log(
      `[${new Date().toISOString()}] Google Drive uploads ENABLED. Target Folder ID: ${GDRIVE_FOLDER_ID}`
    );
    if (!GDRIVE_SERVICE_ACCOUNT_PATH) {
      console.error(
        `[${new Date().toISOString()}] CRITICAL: GDRIVE_SERVICE_ACCOUNT_PATH is not set. Google Drive uploads will fail.`
      );
    } else {
      try {
        await fs.access(GDRIVE_SERVICE_ACCOUNT_PATH);
      } catch (_e) {
        console.error(
          `[${new Date().toISOString()}] CRITICAL: GDRIVE_SERVICE_ACCOUNT_PATH file not found or not accessible at '${GDRIVE_SERVICE_ACCOUNT_PATH}'. Google Drive uploads will fail.`
        );
      }
    }
  } else {
    console.log(`[${new Date().toISOString()}] Google Drive uploads DISABLED.`);
  }

  await fixInvalidRenderStates(); // Run the cleanup once on startup
  await initializeRenderSettings(); // Load render settings

  const workerPromises: Promise<void>[] = [];
  for (let i = 0; i < CONCURRENCY; i++) {
    workerPromises.push(workerLoop(i + 1));
  }

  try {
    await Promise.all(workerPromises); // Keep the main process alive and wait for all workers (though they loop infinitely)
  } catch (err) {
    console.error(
      `[${new Date().toISOString()}] A worker encountered a fatal error and exited (this should not happen with current loop structure):`,
      err
    );
    // Exiting if Promise.all rejects, though individual worker loops are designed to be resilient.
    process.exit(1);
  }
}

// Helper to execute OpenSCAD to render an STL file from a model with parameters
async function execOpenSCAD(
  modelPath: string,
  outputPath: string,
  parameters: Prisma.JsonValue
): Promise<void> {
  console.log(`[${new Date().toISOString()}] execOpenSCAD: Starting render for ${modelPath}`);
  console.log(
    `[${new Date().toISOString()}] execOpenSCAD: Raw parameters:`,
    JSON.stringify(parameters, null, 2)
  );

  // Check if we need to use a specific configuration set from Master.json
  let finalParameters = parameters;
  if (parameters && typeof parameters === 'object' && !Array.isArray(parameters)) {
    const paramObj = parameters as Record<string, unknown>;
    if (paramObj._useConfigSet && typeof paramObj._useConfigSet === 'string') {
      const configSetName = paramObj._useConfigSet;
      console.log(
        `[${new Date().toISOString()}] execOpenSCAD: Using config set '${configSetName}' from Master.json`
      );

      try {
        // Load Master.json configuration
        const masterJsonPath = path.resolve('./Master.json');
        const masterJsonContent = await fs.readFile(masterJsonPath, 'utf-8');
        const masterConfig = JSON.parse(masterJsonContent);

        if (masterConfig.parameterSets && masterConfig.parameterSets[configSetName]) {
          const configSet = masterConfig.parameterSets[configSetName];
          console.log(
            `[${new Date().toISOString()}] execOpenSCAD: Found config set '${configSetName}'`
          );

          // Merge the config set with the original parameters, giving priority to original parameters
          finalParameters = {
            ...configSet,
            ...parameters,
          };

          // Remove the special flag
          delete (finalParameters as Record<string, unknown>)._useConfigSet;

          console.log(
            `[${new Date().toISOString()}] execOpenSCAD: Merged parameters with config set`
          );
        } else {
          console.warn(
            `[${new Date().toISOString()}] execOpenSCAD: Config set '${configSetName}' not found in Master.json`
          );
        }
      } catch (error) {
        console.error(
          `[${new Date().toISOString()}] execOpenSCAD: Error loading Master.json:`,
          error
        );
      }
    }
  }

  // Convert the generic JsonValue parameters to a proper Record object for renderScadToStl
  const vars: Record<string, string | number> = {};

  // Parse the parameters object
  if (finalParameters && typeof finalParameters === 'object' && !Array.isArray(finalParameters)) {
    Object.entries(finalParameters).forEach(([key, value]) => {
      if (typeof value === 'string') {
        // Try to convert string numbers to actual numbers for OpenSCAD
        const numericValue = parseFloat(value);
        if (
          !isNaN(numericValue) &&
          isFinite(numericValue) &&
          value.trim() === numericValue.toString()
        ) {
          vars[key] = numericValue;
          console.log(
            `[${new Date().toISOString()}] execOpenSCAD: Parameter ${key} = ${numericValue} (converted from string)`
          );
        } else {
          vars[key] = value;
          console.log(`[${new Date().toISOString()}] execOpenSCAD: Parameter ${key} = "${value}"`);
        }
      } else if (typeof value === 'number') {
        vars[key] = value;
        console.log(`[${new Date().toISOString()}] execOpenSCAD: Parameter ${key} = ${value}`);
      } else if (value !== null && value !== undefined) {
        // Convert other types to string
        vars[key] = JSON.stringify(value);
        console.log(
          `[${new Date().toISOString()}] execOpenSCAD: Parameter ${key} = ${JSON.stringify(value)} (converted)`
        );
      }
    });
  }

  // Filter out empty string values as a diagnostic step
  const filteredVars: Record<string, string | number> = {};
  for (const [key, value] of Object.entries(vars)) {
    if (typeof value === 'string' && value === '') {
      console.log(
        `[${new Date().toISOString()}] execOpenSCAD: Filtering out empty string parameter: ${key}`
      );
      // Skip this parameter
    } else {
      filteredVars[key] = value;
    }
  }

  // Ensure the output directory exists
  const outputDir = path.dirname(outputPath);
  await fs.mkdir(outputDir, { recursive: true });

  // Log full parameters for debugging
  console.log(
    `[${new Date().toISOString()}] execOpenSCAD: About to call renderScadToStl for ${modelPath}. Output: ${outputPath}`
  );
  console.log(
    `[${new Date().toISOString()}] execOpenSCAD: Filtered parameters being passed: ${JSON.stringify(filteredVars, null, 2)}`
  );

  try {
    // Check for fonts directory and use it if available
    const fontsPath = path.join(process.cwd(), 'fonts');
    try {
      await fs.access(fontsPath);
      console.log(`Using custom fonts from ${fontsPath}`);
    } catch (_e) {
      // Fonts directory doesn't exist, which is fine
    }

    // Use the imported renderScadToStl function to render the model
    await renderScadToStl(modelPath, {
      variables: filteredVars,
      outputDir: outputDir,
      fileName: path.basename(outputPath),
    });
  } catch (error) {
    console.error(`[${new Date().toISOString()}] OpenSCAD failed to render STL:`, error);
    throw new Error(
      `OpenSCAD failed to render STL: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

// Add back getProductFolder and getAlphaFolder
function getProductFolder(sku: string): string {
  if (sku === 'PER-KEY3D-STY3-Y3D' || sku.startsWith('PER-KEY3D')) return 'dual-colours';
  if (sku === 'Y3D-NKC-002' || sku.startsWith('Y3D-NKC-002-')) return 'bubble-style';
  if (sku === 'Y3D-NKC-001' || sku.startsWith('Y3D-NKC-001-')) return 'signature-style';
  if (sku === 'N9-93VU-76VK') return 'new3-tag';
  if (sku === 'Y3D-REGKEY-STL1') return 'reg-keys';
  if (sku.startsWith('PER-2PER-')) return 'cable-clip';
  return 'other';
}

function getAlphaFolder(name: string): string {
  const ch = name.charAt(0).toUpperCase();
  return ch >= 'A' && ch <= 'Z' ? ch : '#';
}

// Function to initialize render settings by loading from JSON file
async function initializeRenderSettings(): Promise<void> {
  const settingsPath = path.resolve('./openscad/render_settings.json'); // Assuming relative to project root
  try {
    console.log(`[${new Date().toISOString()}] Loading render settings from ${settingsPath}...`);
    const fileContent = await fs.readFile(settingsPath, 'utf-8');
    loadedRenderSettings = JSON.parse(fileContent) as RenderSettings;
    if (loadedRenderSettings && loadedRenderSettings.parameterSets) {
      console.log(
        `[${new Date().toISOString()}] Successfully loaded ${Object.keys(loadedRenderSettings.parameterSets).length} parameter sets.`
      );
    } else {
      console.error(
        `[${new Date().toISOString()}] Render settings loaded but structure is invalid or parameterSets is missing.`
      );
      loadedRenderSettings = null;
    }
  } catch (error: unknown) {
    console.error(
      `[${new Date().toISOString()}] Error loading or parsing render_settings.json from ${settingsPath}:`,
      error
    );
    loadedRenderSettings = null;
  }
}

// Helper function to process a single task manually (for --task=ID option)
async function processManualTask(taskId: string): Promise<void> {
  try {
    // Load the task with all required relations
    const task = await prisma.printOrderTask.findUnique({
      where: { id: taskId },
      include: {
        OrderItem: { include: { Product: true } },
        Customer: true,
      },
    });

    if (!task || !task.OrderItem || !task.OrderItem.Product) {
      console.error(
        `[${new Date().toISOString()}] Task ${taskId} not found or missing required relations`
      );
      process.exit(1);
    }

    // Construct the TaskWithProduct object
    const productForTask = task.OrderItem.Product;

    // Determine OpenSCAD model and parameters
    let openscadModelPath: string | null = null;
    let openscadParameters: Prisma.JsonValue = {};

    // Reuse the same logic from reserveTask
    if (productForTask.sku?.startsWith('PER-KEY3D')) {
      openscadModelPath = 'DualColour.scad';
      const customText = task.custom_text || '';
      let lines = { line1: '', line2: '', line3: '' };

      if (customText.match(/\r?\n|\\|\//)) {
        // Explicit delimiters found, use existing logic
        const textLines = customText.split(/\r?\n|\\|\//).map(t => t.trim());
        lines.line1 = textLines[0] || productForTask.name || 'DefaultText';
        lines.line2 = textLines[1] || '';
        lines.line3 = textLines[2] || '';
      } else {
        // No explicit delimiters, use new auto-distribution logic
        lines = autoDistributeText(customText);
        // If customText was empty or only whitespace, autoDistributeText returns all empty.
        // In that case, use product name as default for line1.
        if (!lines.line1 && !lines.line2 && !lines.line3) {
          lines.line1 = productForTask.name || 'DefaultText';
        }
      }

      openscadParameters = {
        line1: lines.line1,
        line2: lines.line2,
        line3: lines.line3,
        // Add font spacing parameters if needed
        font_narrow_widen: -5, // Default to -5 to make text slightly narrower
        character_spacing: 0.95, // Default to 0.95 to bring characters closer together
      };
    } else if (productForTask.sku?.startsWith('REG-KEY')) {
      openscadModelPath = 'registration_keys/reg_key_v2.scad';
      openscadParameters = {
        reg_number: task.custom_text || 'ABC123XYZ',
      };
      console.log(
        `[${new Date().toISOString()}] Task ${task.id} (${productForTask.sku}): Using reg_key_v2.scad with reg_number='${task.custom_text || ''}'`
      );
    } else if (productForTask.sku?.startsWith('PER-2PER-')) {
      openscadModelPath = 'cable_clips/cable_clip.scad';
      const clipText =
        (task.custom_text || '')
          .split(/\r?\n|\\|\//)
          .map(line => line.trim())
          .filter(line => line.length > 0)[0] ||
        productForTask.name ||
        'DefaultText';

      openscadParameters = {
        text: clipText,
        size: 3.5,
      };
    } else if (
      productForTask.sku === 'Y3D-NKC-002' ||
      (productForTask.sku && productForTask.sku.startsWith('Y3D-NKC-002-'))
    ) {
      // Bubble style keychain - uses DualColour.scad with Bubble parameter set from Master.json
      openscadModelPath = 'DualColour.scad';

      const customText = task.custom_text || productForTask.name || 'DefaultText';
      let lines: { line1: string; line2: string; line3: string };

      if (customText.match(/\r?\n|\\|\//)) {
        // Explicit delimiters found, use existing logic
        const textLines = customText.split(/\r?\n|\\|\//).map(t => t.trim());
        lines = {
          line1: textLines[0] || productForTask.name || 'DefaultText',
          line2: textLines[1] || '',
          line3: textLines[2] || '',
        };
      } else {
        // No explicit delimiters, use bubble-specific auto-distribution logic
        lines = autoDistributeTextBubble(customText);
        // If customText was empty or only whitespace, autoDistributeTextBubble returns all empty.
        // In that case, use product name as default for line1.
        if (!lines.line1 && !lines.line2 && !lines.line3) {
          lines.line1 = productForTask.name || 'DefaultText';
        }
      }

      openscadParameters = {
        line1: lines.line1,
        line2: lines.line2,
        line3: lines.line3,
        // Special flag to indicate we want to use the Bubble configuration from Master.json
        _useConfigSet: 'Bubble',
      };
      console.log(
        `[${new Date().toISOString()}] Manual Task ${task.id} (${productForTask.sku}): Using DualColour.scad with Bubble config for lines='${lines.line1}' / '${lines.line2}' / '${lines.line3}'`
      );
    } else if (
      productForTask.sku === 'Y3D-NKC-001' ||
      productForTask.sku?.startsWith('Y3D-NKC-001-')
    ) {
      // Signature style keychain - uses DualColour.scad with Signature parameter set from Master.json
      openscadModelPath = 'DualColour.scad';

      const customText = task.custom_text || productForTask.name || 'DefaultText';

      // Signature style typically uses single line text (like a signature)
      // If there are line breaks, take only the first line
      const signatureText =
        customText.split(/\r?\n|\\|\//).map(t => t.trim())[0] ||
        productForTask.name ||
        'DefaultText';

      openscadParameters = {
        line1: signatureText,
        line2: '',
        line3: '',
        // Special flag to indicate we want to use the Signature configuration from Master.json
        _useConfigSet: 'Signature',
      };
      console.log(
        `[${new Date().toISOString()}] Manual Task ${task.id} (${productForTask.sku}): Using DualColour.scad with Signature config for text='${signatureText}'`
      );
    } else if (productForTask.sku === 'Y3D-REGKEY-STL1') {
      // Registration key style - uses RegKey.scad with Text parameter
      openscadModelPath = 'RegKey.scad';

      const customText = task.custom_text || productForTask.name || 'ABC 123';

      // RegKey.scad expects a single "Text" parameter
      openscadParameters = {
        Text: customText,
      };

      console.log(
        `[${new Date().toISOString()}] Manual Task ${task.id} (${productForTask.sku}): Using RegKey.scad with Text='${customText}'`
      );
    } else {
      console.error(
        `[${new Date().toISOString()}] Task ${task.id} has unsupported SKU: ${productForTask.sku}`
      );
      process.exit(1);
    }

    const taskWithProduct: TaskWithProduct = {
      ...task,
      OrderItem: task.OrderItem as OrderItem & { Product: Product },
      Customer: task.Customer,
      resolvedProductInfo: {
        sku: productForTask.sku || 'unknown_sku',
        name: productForTask.name || 'Unknown Product',
        openscad_model_path: openscadModelPath,
        openscad_parameters: openscadParameters,
      },
    };

    // Process the task
    console.log(`[${new Date().toISOString()}] Processing manual task ${taskId}...`);
    await processTask(taskWithProduct);
    console.log(`[${new Date().toISOString()}] Manual task ${taskId} completed successfully`);
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error processing manual task ${taskId}:`, error);
    process.exit(1);
  }
}

// Helper function to reset eligible tasks to pending state (for --refresh option)
async function resetTasksToPending(): Promise<void> {
  try {
    // Find all tasks that are in-progress or failed but have not exceeded retry limit
    const result = await prisma.printOrderTask.updateMany({
      where: {
        OR: [
          { stl_render_state: PrintOrderTask_stl_render_state.running }, // Reset stuck "running" tasks
          {
            stl_render_state: PrintOrderTask_stl_render_state.failed,
            render_retries: { lt: MAX_RETRIES }, // Only reset tasks that haven't exceeded retry limit
          },
        ],
        // Removed the orderItem filter conditions since it was causing schema validation issues
        // We'll resettle all eligible tasks regardless of order status
      },
      data: {
        stl_render_state: PrintOrderTask_stl_render_state.pending,
        updated_at: new Date(),
      },
    });

    console.log(`[${new Date().toISOString()}] Reset ${result.count} tasks to pending state`);
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error resetting tasks:`, error);
    process.exit(1);
  }
}

// Helper function to clear all files in the Google Drive folder
async function clearGDriveFiles(): Promise<void> {
  if (!GDRIVE_ENABLED || !GDRIVE_FOLDER_ID) {
    console.error(
      `[${new Date().toISOString()}] Google Drive is not enabled or GDRIVE_FOLDER_ID is not set.`
    );
    return;
  }

  try {
    // Initialize Google Drive client with service account auth
    const auth = new google.auth.GoogleAuth({
      keyFile: GDRIVE_SERVICE_ACCOUNT_PATH,
      scopes: ['https://www.googleapis.com/auth/drive'],
    });
    const drive = google.drive({ version: 'v3', auth });

    // Get all files in the target folder (recursive)
    await clearFolderContents(drive, GDRIVE_FOLDER_ID);
    console.log(
      `[${new Date().toISOString()}] Successfully cleared Google Drive folder: ${GDRIVE_FOLDER_ID}`
    );
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error clearing Google Drive files:`, error);
    throw error;
  }
}

// Helper function to recursively clear folder contents
async function clearFolderContents(
  drive: ReturnType<typeof google.drive>,
  folderId: string
): Promise<void> {
  try {
    // First, find all files and folders in this folder
    console.log(`[${new Date().toISOString()}] Listing contents of folder: ${folderId}`);

    let pageToken: string | undefined;
    let totalDeleted = 0;

    do {
      // List all files and folders in the current directory
      const response = await drive.files.list({
        q: `'${folderId}' in parents and trashed = false`,
        pageSize: 100,
        fields: 'nextPageToken, files(id, name, mimeType)',
        pageToken: pageToken || undefined,
      });

      const files = response.data.files || [];
      pageToken = response.data.nextPageToken || undefined;

      if (files.length === 0) {
        console.log(`[${new Date().toISOString()}] No files found in folder: ${folderId}`);
        continue;
      }

      console.log(
        `[${new Date().toISOString()}] Found ${files.length} items in folder: ${folderId}`
      );

      // Process each file/folder
      for (const file of files) {
        if (!file.id) {
          console.warn(
            `[${new Date().toISOString()}] File missing ID, skipping: ${file.name || 'unknown'}`
          );
          continue;
        }

        // If it's a folder, recursively clear its contents first
        if (file.mimeType === 'application/vnd.google-apps.folder') {
          console.log(
            `[${new Date().toISOString()}] Processing subfolder: ${file.name} (${file.id})`
          );
          await clearFolderContents(drive, file.id);
        }

        // Delete the file or folder
        console.log(`[${new Date().toISOString()}] Deleting: ${file.name} (${file.id})`);
        await drive.files.delete({ fileId: file.id });
        totalDeleted++;
      }
    } while (pageToken);

    console.log(
      `[${new Date().toISOString()}] Successfully deleted ${totalDeleted} items from folder: ${folderId}`
    );
  } catch (error) {
    console.error(`[${new Date().toISOString()}] Error clearing folder ${folderId}:`, error);
    throw error;
  }
}

main().catch(err => {
  console.error('[FINAL CATCH] STL Render Worker CRASHED UNEXPECTEDLY:', err);
  process.exit(1);
});
