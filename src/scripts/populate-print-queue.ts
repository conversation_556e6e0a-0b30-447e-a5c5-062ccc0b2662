'use strict';
// Node built-in modules first
import fsCallback from 'node:fs'; // For createWriteStream
import fs from 'node:fs/promises';
import path from 'node:path';
import util from 'node:util';

// External dependencies
import { PrismaClient } from '@prisma/client';
import { Command } from 'commander';
import { config } from 'dotenv';
import fetch from 'node-fetch';
// eslint-disable-next-line import/no-named-as-default, import/no-named-as-default-member
import pino from 'pino';
import { z } from 'zod';
import type { Response as FetchResponse } from 'node-fetch'; // Type for node-fetch response

// ShipStation API and other specific ../lib imports
import { fetchShipStationOrderDetails } from '../lib/shipstation/api';
import {
  DEFAULT_OPENAI_MODEL,
  DEFAULT_SYSTEM_PROMPT_PATH,
  DEFAULT_USER_PROMPT_PATH,
  MAX_AI_TOKENS,
  OPENAI_API_URL,
  PRISMA_TRANSACTION_MAX_WAIT,
  PRISMA_TRANSACTION_TIMEOUT,
  SCRIPT_LOG_DIR,
} from '../lib/constants';
import {
  fixInvalidStlRenderStatus,
  getOrdersToProcess,
  OrderWithItemsAndProducts,
} from '../lib/order-processing';
import { fetchAndProcessAmazonCustomization } from '../lib/orders/amazon/customization';

// Local script-specific types and operations (./)
import type {
  AiOrderItemData,
  AiPromptData,
  ApiPayload,
  DirectExtractionResult,
  OrderDebugInfo,
  PrintSettingOption,
  PrintSettings,
  ProcessingOptions,
} from './populate-print-queue.types.js';
import {
  AiOrderResponseSchema,
  PersonalizationDetailSchema,
} from './populate-print-queue.types.js';
import {
  createOrUpdateTasksInTransaction,
  syncExistingTasksToShipstation,
} from './database/task-operations.js';

// Internal/local aliased imports (@/)
import { productNameMappings, simplifyProductName } from '@/lib/product-mapping';

// --- Constants --- (These are now imported)
// const OPENAI_API_URL = ...;
// ... (remove all duplicated constant definitions here) ...

// --- Helper: Parse bulk personalization notes ---
// parseBulkPersonalizationNotes function moved to ./database/task-operations.js

// Initialize single Prisma Client instance
const prisma = new PrismaClient();
const isPrismaConnected = false;

// Load environment variables
config();

// Module-level logger for very early use or if main logger setup fails
let logger: pino.Logger = pino({ level: 'warn' }); // Initial basic logger

// All types and schemas are now imported from the separate types file

// --- Define local interface for Amazon Personalization Data ---
// // Define local interface for Amazon Personalization Data (Unused)
// interface AmazonPersonalization {
//   text: string | null;
//   color1: string | null;
//   color2: string | null;
//   // Add other fields if known, e.g., quantity, sku, etc.
// }

// --- Helper Functions ---
// Modify helpers to accept a logger instance
async function loadPromptFile(filePath: string): Promise<string> {
  try {
    return await fs.readFile(filePath, 'utf-8');
  } catch (error: unknown) {
    const errorMsg = error instanceof Error ? error.message : 'Unknown file load error';
    logger.error({ filePath, err: error }, `Failed to load prompt file: ${filePath} - ${errorMsg}`);
    throw new Error(`Could not load prompt file: ${filePath}`);
  }
}

async function appendToDebugLog(filePath: string | undefined, data: OrderDebugInfo): Promise<void> {
  if (!filePath) return;
  try {
    const logEntry = `\n--- Entry: ${new Date().toISOString()} ---\n${util.inspect(data, { depth: null, colors: false })}\n`;
    await fs.appendFile(filePath, logEntry);
  } catch (error: unknown) {
    const errorMsg = error instanceof Error ? error.message : 'Unknown debug log write error';
    logger.error(
      { filePath, err: error },
      `Failed to write to debug log file ${filePath}: ${errorMsg}`
    );
  }
}

// confirmExecution function moved to ./database/task-operations.js

let runLogId: number | null = null;
const createRunLog = async (data: { scriptName: string }) => {
  logger.info({ data }, '[Mock Log] Create Run Log:');
  runLogId = Date.now();
  return { id: runLogId };
};
const updateRunLog = async (id: number | null, data: { status: string; message?: string }) => {
  logger.info({ id, data }, '[Mock Log] Update Run Log:');
};

// --- Helper Function for ShipStation Personalized Details String ---
function buildPersonalizedDetailsString(
  personalizations: Array<z.infer<typeof PersonalizationDetailSchema>>,
  lineItemKeyForLog: string,
  orderIdForLog: number
): string {
  if (!personalizations || personalizations.length === 0) {
    logger.info(
      `[Util][Order ${orderIdForLog}][Item ${lineItemKeyForLog}] No personalizations to build details string from.`
    );
    return 'No personalization details extracted.';
  }
  const personalizedDetailStrings: string[] = [];
  for (const p of personalizations) {
    const text = p.customText || 'N/A';
    const color1 = p.color1;
    const color2 = p.color2 ? ` / ${p.color2}` : '';
    personalizedDetailStrings.push(`${text} (${color1 || 'N/A'}${color2})`);
  }
  let combinedDetailsString = personalizedDetailStrings.join(', ');
  const MAX_LEN_SHIPSTATION_OPTION = 200;
  const TRUNCATION_SUFFIX = '... (See Packing List)';
  if (combinedDetailsString.length > MAX_LEN_SHIPSTATION_OPTION) {
    combinedDetailsString =
      combinedDetailsString.substring(0, MAX_LEN_SHIPSTATION_OPTION - TRUNCATION_SUFFIX.length) +
      TRUNCATION_SUFFIX;
  }
  logger.info(
    `[Util][Order ${orderIdForLog}][Item ${lineItemKeyForLog}] Built personalized details string: "${combinedDetailsString}"`
  );
  return combinedDetailsString;
}

// --- Replicated Helper Function ---
// PrintSettingOption and PrintSettings types are now imported from the types file

// Function to extract the customization URL from item print_settings
function extractCustomizationUrl(
  item: OrderWithItemsAndProducts['OrderItem'][number]
): string | null {
  const printSettings: PrintSettings = item.print_settings as PrintSettings;
  // Log the print_settings being processed by this function at a debug level
  logger.debug(
    {
      orderId: item.orderId, // Assuming OrderItem has orderId, if not, this needs context
      itemId: item.id,
      shipstationLineItemKey: item.shipstationLineItemKey,
      printSettings,
    },
    `[extractCustomizationUrl] Processing item ${item.id} with print_settings.`
  );

  if (!printSettings) {
    logger.debug(`[extractCustomizationUrl][Item ${item.id}] No print_settings found.`);
    return null;
  }

  // Helper to check for the URL setting, case-insensitive for the name
  const isUrlSetting = (setting: unknown): setting is PrintSettingOption =>
    setting !== null &&
    typeof setting === 'object' &&
    !Array.isArray(setting) &&
    'name' in setting &&
    typeof (setting as PrintSettingOption).name === 'string' &&
    (setting as PrintSettingOption).name.toLowerCase() === 'customizedurl' && // Case-insensitive check
    'value' in setting &&
    typeof (setting as PrintSettingOption).value === 'string';

  if (Array.isArray(printSettings)) {
    const urlSetting = printSettings.find(isUrlSetting);
    return urlSetting ? urlSetting.value : null;
  } else if (typeof printSettings === 'object' && printSettings !== null) {
    // Check direct object property case-insensitively
    const record = printSettings as Record<string, unknown>; // Use unknown for initial dynamic access
    const key = Object.keys(record).find(k => k.toLowerCase() === 'customizedurl'); // Find key case-insensitively
    if (key && typeof record[key] === 'string') {
      return record[key] as string;
    }
    // Fallback check using the isUrlSetting helper (for objects structured like { name: '...', value: '...' })
    if (isUrlSetting(printSettings)) {
      return printSettings.value;
    }
  }
  logger.debug(
    `[extractCustomizationUrl][Item ${item.id}] No CustomizedURL found in print_settings.`
  );
  return null;
}

// --- NEW HELPER FUNCTIONS (Amazon Specific Data Extraction) ---
// DirectExtractionResult interface is now imported from the types file

async function extractDirectItemData(
  order: OrderWithItemsAndProducts,
  item: OrderWithItemsAndProducts['OrderItem'][number],
  product: OrderWithItemsAndProducts['OrderItem'][number]['Product']
): Promise<DirectExtractionResult> {
  logger.info(
    { orderId: order.id, itemId: item.id, shipstationLineItemKey: item.shipstationLineItemKey },
    `[DirectExtract] Entered for item.`
  );
  // --- Amazon URL Extraction Logic ---
  const isAmazon = order.marketplace?.toLowerCase().includes('amazon');
  logger.info(
    {
      orderId: order.id,
      itemId: item.id,
      marketplace: order.marketplace,
      isAmazonResult: isAmazon,
    },
    `[DirectExtract] Checked isAmazon.`
  );
  logger.debug(
    `[DirectExtract][Order ${order.id}][Item ${item.id}] Marketplace='${order.marketplace}', IsAmazon=${isAmazon}`
  );

  if (isAmazon) {
    // Log the raw print_settings for the item when it's an Amazon order
    logger.info(
      {
        orderId: order.id,
        itemId: item.id,
        shipstationLineItemKey: item.shipstationLineItemKey,
        printSettingsFromItem: item.print_settings,
      },
      `[DirectExtract][Order ${order.id}][Item ${item.id}] Amazon item. Raw print_settings before calling extractCustomizationUrl.`
    );

    const amazonUrl = extractCustomizationUrl(item);
    logger.debug(
      `[DirectExtract][Order ${order.id}][Item ${item.id}] Extracted amazonUrl='${amazonUrl}'`
    );

    if (amazonUrl) {
      logger.info(
        `[DB][Order ${order.id}][Item ${item.id}] Found Amazon CustomizedURL. Attempting to fetch...`
      );
      try {
        const amazonData = await fetchAndProcessAmazonCustomization(amazonUrl);
        logger.debug(
          `[DirectExtract][Order ${order.id}][Item ${item.id}] fetchAndProcessAmazonCustomization returned: ${JSON.stringify(amazonData)}`
        );

        if (amazonData) {
          logger.info(
            `[DB][Order ${order.id}][Item ${item.id}] Successfully processed Amazon URL.`
          );
          let processedCustomText = amazonData.customText;
          if (product?.sku?.toUpperCase().includes('REGKEY') && processedCustomText) {
            processedCustomText = processedCustomText.toUpperCase();
            logger.info(
              `[DB][Order ${order.id}][Item ${item.id}] REGKEY SKU detected, upper-casing custom text to '${processedCustomText}'.`
            );
          }
          return {
            customText: processedCustomText,
            color1: amazonData.color1,
            color2: amazonData.color2,
            dataSource: 'AmazonURL',
            annotation: 'Data from Amazon CustomizedURL',
          };
        } else {
          logger.warn(
            `[DB][Order ${order.id}][Item ${item.id}] Failed to process Amazon URL (fetch function returned null/undefined). Will fall back to AI.`
          );
          // Fall through to AI fallback by returning dataSource: null
        }
      } catch (amazonError) {
        const errorMsg = amazonError instanceof Error ? amazonError.message : String(amazonError);
        logger.error(
          { err: amazonError, orderId: order.id, itemId: item.id },
          `[DB][Order ${order.id}][Item ${item.id}] Error during fetchAndProcessAmazonCustomization: ${errorMsg}`
        );
        return {
          customText: null,
          color1: null,
          color2: null,
          dataSource: null, // Indicates fallback to AI needed due to error
          annotation: `Error processing Amazon URL: ${errorMsg}`.substring(0, 1000),
          needsReview: true,
          reviewReason: `Amazon URL Error: ${errorMsg}`.substring(0, 255),
        };
      }
    } else {
      logger.debug(
        `[DirectExtract][Order ${order.id}][Item ${item.id}] Amazon order but CustomizedURL extraction returned null. Will fall back to AI.`
      );
      // Fall through to AI fallback by returning dataSource: null
    }
  } else {
    logger.debug(
      `[DirectExtract][Order ${order.id}][Item ${item.id}] Not identified as Amazon marketplace. Will fall back to AI.`
    );
    // Fall through to AI fallback by returning dataSource: null
  }

  // Default fallback: indicates that AI processing is needed
  return {
    customText: null,
    color1: null,
    color2: null,
    dataSource: null,
    annotation: 'Needs AI processing',
  };
}

// --- AI Extraction Logic (Order Level) --- Replace Placeholder
async function extractOrderPersonalization(
  order: OrderWithItemsAndProducts,
  options: Pick<
    ProcessingOptions,
    | 'openaiApiKey'
    | 'openaiModel'
    | 'systemPrompt'
    | 'userPromptTemplate'
    | 'forceRecreate'
    | 'preserveText'
    | 'dryRun'
  >
): Promise<{
  success: boolean;
  aiResponseData?: z.infer<typeof AiOrderResponseSchema>; // Renamed from data to aiResponseData
  itemsSentToAi: AiOrderItemData[]; // Added this to return the itemsForPrompt array
  error?: string;
  promptUsed: string | null;
  rawResponse: string | null;
  modelUsed: string | null;
  liveCustomerNotes?: string | null;
  liveInternalNotes?: string | null;
}> {
  let liveCustomerNotes: string | null | undefined = null;
  let liveInternalNotes: string | null | undefined = null;
  // type OrderItemWithProduct = Prisma.OrderItemGetPayload<{ include: { product: true } }>; // Removed unused type

  const itemsForPrompt: AiOrderItemData[] = []; // Initialize array

  for (const orderItem of order.OrderItem) {
    if (orderItem.shipstationLineItemKey == null) {
      logger.warn(
        { orderId: order.id, itemId: orderItem.id },
        'Skipping item with null shipstationLineItemKey for AI prompt.'
      );
      continue;
    }

    // Use simplified name for AI prompt only - this doesn't change the actual product name in database
    const simplifiedName = simplifyProductName(orderItem.Product?.name ?? '', productNameMappings);

    const currentItemData: AiOrderItemData = {
      id: orderItem.shipstationLineItemKey,
      sku: orderItem.Product?.sku ?? '',
      name: simplifiedName,
      quantity: orderItem.quantity,
      options: [], // Initialize options
      productName: orderItem.Product?.name,
      productId: orderItem.Product?.id,
    };

    // Amazon Customization URL Check
    const customizedUrl = extractCustomizationUrl(orderItem);
    if (customizedUrl && order.marketplace?.toLowerCase() === 'amazon') {
      // Made marketplace check case-insensitive
      logger.info(
        { orderId: order.id, itemId: orderItem.id, url: customizedUrl },
        `[AI Prep] Found Amazon customization URL for item ${orderItem.id}. Fetching...`
      );
      try {
        const amazonDataResult = await fetchAndProcessAmazonCustomization(customizedUrl);
        // const amazonDataArray = amazonDataResult as unknown as AmazonPersonalization[] | undefined; // REMOVE THIS CAST

        if (amazonDataResult) {
          // Check if amazonDataResult is not null
          // const firstAmazonPersonalization = amazonDataArray[0]; // REMOVE THIS
          currentItemData._amazonDataProcessed = true;
          currentItemData._amazonCustomText = amazonDataResult.customText; // USE DIRECTLY
          currentItemData._amazonColor1 = amazonDataResult.color1; // USE DIRECTLY
          currentItemData._amazonColor2 = amazonDataResult.color2; // USE DIRECTLY
          currentItemData._amazonDataSource = 'AmazonURL';
          logger.info(
            {
              orderId: order.id,
              itemId: orderItem.id,
              extracted: {
                text: amazonDataResult.customText,
                color1: amazonDataResult.color1,
                color2: amazonDataResult.color2,
              },
            },
            `[AI Prep] Successfully extracted data directly from Amazon URL for item ${orderItem.id}. This data will be prioritized.`
          );

          // Still populate currentItemData.options for the AI prompt as a fallback or for context,
          // but the _amazon flags will ensure we use the direct data.
          currentItemData.options = [
            { name: 'Name or Text', value: amazonDataResult.customText || '' },
            { name: 'Colour 1', value: amazonDataResult.color1 || '' },
          ];
          if (amazonDataResult.color2) {
            currentItemData.options.push({ name: 'Colour 2', value: amazonDataResult.color2 });
          }
        } else {
          logger.warn(
            { orderId: order.id, itemId: orderItem.id },
            `[AI Prep] Amazon customization URL for item ${orderItem.id} yielded no data (fetchAndProcessAmazonCustomization returned null).`
          );
        }
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        logger.error(
          { orderId: order.id, itemId: orderItem.id, err: errorMsg },
          `[AI Prep] Error processing Amazon customization URL for item ${orderItem.id}: ${errorMsg}`
        );
        // Do not set _amazonDataProcessed = true if fetching failed
      }
    } else {
      // Populate options from print_settings if not an Amazon URL case or URL processing failed
      let mappedOptions: Array<{ name: string; value: string }> = [];
      if (orderItem.print_settings) {
        if (Array.isArray(orderItem.print_settings)) {
          try {
            mappedOptions = (orderItem.print_settings as unknown as PrintSettingOption[])
              .map(opt => ({
                name: String(opt.name ?? ''),
                value: String(opt.value ?? ''),
              }))
              .filter(opt => opt.name && opt.value);
          } catch (e) {
            logger.warn(
              {
                orderId: order.id,
                itemId: orderItem.id,
                printSettings: orderItem.print_settings,
                error: e,
              },
              `[AI Prep] Could not parse array print_settings for item ${orderItem.id}`
            );
          }
        } else if (
          typeof orderItem.print_settings === 'object' &&
          orderItem.print_settings !== null &&
          'options' in orderItem.print_settings &&
          Array.isArray((orderItem.print_settings as { options: unknown[] }).options)
        ) {
          try {
            mappedOptions = (
              (orderItem.print_settings as { options: unknown[] })
                .options as unknown as PrintSettingOption[]
            )
              .map(opt => ({
                name: String(opt.name ?? ''),
                value: String(opt.value ?? ''),
              }))
              .filter(opt => opt.name && opt.value);
          } catch (e) {
            logger.warn(
              {
                orderId: order.id,
                itemId: orderItem.id,
                printSettings: orderItem.print_settings,
                error: e,
              },
              `[AI Prep] Could not parse object.options print_settings for item ${orderItem.id}`
            );
          }
        } else if (
          typeof orderItem.print_settings === 'object' &&
          orderItem.print_settings !== null
        ) {
          // Handle case where print_settings is an object but not the expected array or {options: array}
          logger.warn(
            { orderId: order.id, itemId: orderItem.id, printSettings: orderItem.print_settings },
            `[AI Prep] Unhandled print_settings object structure for item ${orderItem.id}`
          );
        }
      }
      currentItemData.options = mappedOptions;
    }
    itemsForPrompt.push(currentItemData);
  }

  if (itemsForPrompt.length === 0 && !order.customer_notes && !order.internal_notes) {
    logger.info(
      `[AI][Order ${order.id}] No items with lineItemKeys found to send to AI, or all items lack product info for prompt.`
    );
    return {
      success: true, // Or false, depending on desired behavior for no items
      itemsSentToAi: itemsForPrompt, // Return empty or processed itemsForPrompt
      promptUsed: null,
      rawResponse: 'No items sent to AI.',
      modelUsed: null,
      liveCustomerNotes,
      liveInternalNotes,
    };
  }

  // --- Enhancement: Fetch live ShipStation notes if --force-recreate is set or DB notes are empty ---
  if (
    (options.forceRecreate || !order.customer_notes || !order.internal_notes) &&
    order.shipstation_order_id &&
    process.env.SHIPSTATION_API_KEY &&
    process.env.SHIPSTATION_API_SECRET
  ) {
    try {
      logger.info(
        { orderId: order.id, shipstationOrderId: order.shipstation_order_id },
        '[LiveNotes] Attempting to fetch live notes from ShipStation...'
      );
      const liveNotesResult = await fetchShipStationOrderDetails(
        process.env.SHIPSTATION_API_KEY,
        process.env.SHIPSTATION_API_SECRET,
        order.shipstation_order_id
      );
      if (liveNotesResult) {
        liveCustomerNotes = liveNotesResult.customerNotes;
        liveInternalNotes = liveNotesResult.internalNotes;
        logger.info(
          {
            orderId: order.id,
            shipstationOrderId: order.shipstation_order_id,
            liveCustomerNotes,
            liveInternalNotes,
          },
          '[LiveNotes] Successfully fetched live notes from ShipStation.'
        );
      } else {
        logger.warn(
          { orderId: order.id, shipstationOrderId: order.shipstation_order_id },
          '[LiveNotes] Failed to fetch live notes from ShipStation (API returned null).'
        );
      }
    } catch (err) {
      logger.error(
        { orderId: order.id, shipstationOrderId: order.shipstation_order_id, err },
        '[LiveNotes] Error fetching live notes from ShipStation.'
      );
    }
  }

  const inputData: AiPromptData = {
    orderId: order.id, // Changed to be the numeric database ID
    orderNumber: order.shipstation_order_number ?? 'N/A', // Provide default if null
    orderDate: order.order_date ? order.order_date.toISOString() : new Date().toISOString(), // Handle potential null before toISOString()
    marketplace: order.marketplace ?? 'Unknown',
    customerNotes: liveCustomerNotes !== undefined ? liveCustomerNotes : order.customer_notes,
    internalNotes: liveInternalNotes !== undefined ? liveInternalNotes : order.internal_notes,
    items: itemsForPrompt,
    shippingAddress: {
      name: order.Customer?.name ?? '',
      street1: order.Customer?.street1 ?? '',
      street2: order.Customer?.street2 ?? '',
      city: order.Customer?.city ?? '',
      state: order.Customer?.state ?? '',
      postalCode: order.Customer?.postal_code ?? '',
      country: order.Customer?.country_code ?? '', // Use country_code from Customer
      phone: order.Customer?.phone ?? '',
    },
  };

  const inputDataJson = JSON.stringify(inputData, null, 2);
  const userPromptContent = options.userPromptTemplate.replace('{INPUT_DATA_JSON}', inputDataJson);
  let systemPromptContent = options.systemPrompt;

  // Conditionally modify the system prompt if forceRecreate is true
  if (options.forceRecreate) {
    let forceRecreateInstruction = `\n\nIMPORTANT: The user is manually forcing the recreation of these tasks (force-recreate flag is active).`;
    if (options.preserveText) {
      forceRecreateInstruction += ` The 'customText' field will be preserved from existing data, so focus on accurately extracting other details like colors and quantities. Do not flag items for review based on customText ambiguity if it seems complex but present; assume it is correct. When re-evaluating, if customer notes provide a specific color for a name that strongly conflicts with the Gender-Based Name Color Assignment Guidelines (e.g., a typically female name with 'Red', or a typically male name with 'Pink'), you should DISREGARD the conflicting color from the customer notes for that specific name, even though text is preserved. Instead, assign a color strictly based on the Gender-Based Name Color Assignment Guidelines. Flag this action for review by setting 'needsReview: true' and 'reviewReason: "Force-recreate (preserve text): Disregarded conflicting customer note color '[OriginalColorFromNote]' for name '[Name]' and applied gender-guideline color '[NewGuidelineColor]'. Verify."'.`;
    } else {
      forceRecreateInstruction += ` Re-evaluate all personalizations from scratch. When re-evaluating, if customer notes provide a specific color for a name that strongly conflicts with the Gender-Based Name Color Assignment Guidelines (e.g., a typically female name with 'Red', or a typically male name with 'Pink'), you should DISREGARD the conflicting color from the customer notes for that specific name. Instead, assign a color strictly based on the Gender-Based Name Color Assignment Guidelines. Flag this action for review by setting 'needsReview: true' and 'reviewReason: "Force-recreate: Disregarded conflicting customer note color '[OriginalColorFromNote]' for name '[Name]' and applied gender-guideline color '[NewGuidelineColor]'. Verify."'. Do NOT flag other items for review (set needsReview: false) unless there is critical missing information that completely prevents processing.`;
    }
    systemPromptContent += forceRecreateInstruction;
    logger.info(
      {
        orderId: order.id,
        forceRecreate: options.forceRecreate,
        preserveText: options.preserveText,
      },
      `[AI][Order ${order.id}] Appended force-recreate instruction to system prompt.`
    );
  }

  const fullPromptForDebug = `System:\n${systemPromptContent}\n\nUser:\n${userPromptContent}`;

  logger.debug(`[AI][Order ${order.id}] Preparing extraction...`);
  logger.trace(`[AI][Order ${order.id}] Input Data JSON:\n${inputDataJson}`);
  logger.debug(
    `[AI][Order ${order.id}] Prompt lengths: System=${systemPromptContent.length}, User=${userPromptContent.length}`
  );
  // Avoid logging full prompts at debug level if they are large or sensitive
  // logger.debug(`[AI][Order ${order.id}] System Prompt:\n${systemPromptContent}`);
  // logger.debug(`[AI][Order ${order.id}] User Prompt:\n${options.userPromptTemplate.replace('{INPUT_DATA_JSON}', inputDataJson)}`);

  // API interfaces are now imported from the types file

  let rawResponse: string | null = null;
  const modelUsed = options.openaiModel;
  const apiKey = options.openaiApiKey;
  const startTime = Date.now();

  try {
    if (!apiKey) throw new Error('OpenAI API key missing');

    logger.info(`[AI][Order ${order.id}] Calling OpenAI (${modelUsed})...`);

    const apiPayload: ApiPayload = {
      model: modelUsed,
      messages: [
        { role: 'system', content: systemPromptContent },
        { role: 'user', content: userPromptContent },
      ],
      temperature: 0.0,
      top_p: 1.0,
      frequency_penalty: 0.0,
      presence_penalty: 0.0,
      max_tokens: MAX_AI_TOKENS,
      response_format: { type: 'json_object' },
    };

    logger.debug(
      { provider: 'openai', url: OPENAI_API_URL, headers: 'Authorization HIDDEN' }, // Redacted headers
      `[AI][Order ${order.id}] Sending API Request`
    );
    logger.trace(`[AI][Order ${order.id}] Payload: ${JSON.stringify(apiPayload)}`);

    const response = (await fetch(OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`,
      },
      body: JSON.stringify(apiPayload),
    })) as FetchResponse;

    const duration = Date.now() - startTime;
    logger.info(
      `[AI][Order ${order.id}] Call response status: ${response.status} (${duration}ms).`
    );

    if (!response.ok) {
      const errorBody = await response.text();
      logger.error(
        { status: response.status, body: errorBody },
        `[AI][Order ${order.id}] API error`
      );
      throw new Error(`OpenAI API error ${response.status}: ${errorBody}`);
    }

    const result = await response.json();
    logger.debug({ response: result }, `[AI][Order ${order.id}] API Raw Response Object`);

    // Log token usage if available
    if (result.usage) {
      logger.info(
        {
          orderId: order.id,
          promptTokens: result.usage.prompt_tokens,
          completionTokens: result.usage.completion_tokens,
          totalTokens: result.usage.total_tokens,
          modelUsed: modelUsed,
        },
        `[AI][Order ${order.id}] Token usage`
      );
    }

    rawResponse = result.choices?.[0]?.message?.content?.trim() ?? null;

    if (rawResponse) {
      logger.info(
        `[AI][Order ${order.id}] Extracted Raw Response Content (first 500 chars): ${rawResponse.substring(0, 500)}`
      );
    } else {
      logger.warn(`[AI][Order ${order.id}] Raw Response Content IS NULL after parsing choices.`);
    }

    if (!rawResponse) {
      logger.warn({ result }, `[AI][Order ${order.id}] OpenAI returned empty response content.`);
      throw new Error('OpenAI returned empty response content.');
    }
    logger.debug(`[AI][Order ${order.id}] RAW RESPONSE Content:\n${rawResponse}`);

    let responseJson: unknown;
    try {
      const cleanedContent = rawResponse.replace(/^```json\n?/, '').replace(/\n?```$/, '');
      const contentToProcess = cleanedContent;
      // Add JSON fixing logic if needed (from previous versions)
      // ... (Truncated JSON fixing logic can be re-inserted here if necessary)
      responseJson = JSON.parse(contentToProcess);
      logger.debug(`[AI][Order ${order.id}] Parsed JSON response.`);
    } catch (e) {
      logger.error({ err: e, rawResponse }, `[AI][Order ${order.id}] Failed to parse AI JSON`);
      throw new Error(`Failed to parse AI JSON: ${(e as Error).message}.`);
    }

    const validationResult = AiOrderResponseSchema.safeParse(responseJson);
    if (!validationResult.success) {
      const errorString = JSON.stringify(validationResult.error.format(), null, 2);
      logger.error(`[AI][Order ${order.id}] Zod validation failed: ${errorString}`);
      throw new Error(`AI response validation failed: ${errorString}`);
    }

    logger.info(`[AI][Order ${order.id}] AI response validated.`);

    if (!options.dryRun) {
      try {
        const tasksGenerated = Object.values(validationResult.data.itemPersonalizations).reduce(
          (sum, item) => sum + item.personalizations.length,
          0
        );
        const needsReviewCount = Object.values(validationResult.data.itemPersonalizations).reduce(
          (sum, item) => sum + (item.overallNeedsReview ? 1 : 0),
          0
        );
        await prisma.aiCallLog.create({
          data: {
            id: crypto.randomUUID(),
            scriptName: 'populate-print-queue',
            orderId: order.id,
            orderNumber: order.shipstation_order_number || null,
            marketplace: order.marketplace || null,
            aiProvider: 'openai',
            modelUsed: modelUsed || 'unknown',
            promptSent: fullPromptForDebug, // Consider truncating if too long
            rawResponse: rawResponse, // Consider truncating if too long
            processingTimeMs: Date.now() - startTime,
            success: true,
            tasksGenerated,
            needsReviewCount,
            // tokenUsagePrompt: result.usage?.prompt_tokens, // Example if DB field existed
            // tokenUsageCompletion: result.usage?.completion_tokens, // Example if DB field existed
            // tokenUsageTotal: result.usage?.total_tokens, // Example if DB field existed
          },
        });
        logger.debug(
          {
            orderId: order.id,
            forceRecreate: options.forceRecreate,
            preserveText: options.preserveText,
          },
          `[AI][Order ${order.id}] AI call logged to database with processing flags.`
        );
      } catch (logError) {
        logger.error(
          `[AI][Order ${order.id}] Failed to log AI call to database: ${logError instanceof Error ? logError.message : String(logError)}`
        );
      }
    }

    return {
      success: true,
      aiResponseData: validationResult.data,
      itemsSentToAi: itemsForPrompt,
      promptUsed: fullPromptForDebug,
      rawResponse,
      modelUsed,
    };
  } catch (error: unknown) {
    const errorMsg = error instanceof Error ? error.message : 'Unknown AI extraction error';
    logger.error(`[AI][Order ${order.id}] Extraction failed: ${errorMsg}`, error);

    if (!options.dryRun) {
      try {
        await prisma.aiCallLog.create({
          data: {
            id: crypto.randomUUID(),
            scriptName: 'populate-print-queue',
            orderId: order.id,
            orderNumber: order.shipstation_order_number || null,
            marketplace: order.marketplace || null,
            aiProvider: 'openai',
            modelUsed: modelUsed || 'unknown',
            promptSent: fullPromptForDebug, // Consider truncating
            rawResponse: rawResponse || '', // Consider truncating
            processingTimeMs: Date.now() - startTime,
            success: false,
            errorMessage: errorMsg,
            tasksGenerated: 0,
            needsReviewCount: 0,
            // tokenUsagePrompt: result.usage?.prompt_tokens, // Example if DB field existed
            // tokenUsageCompletion: result.usage?.completion_tokens, // Example if DB field existed
            // tokenUsageTotal: result.usage?.total_tokens, // Example if DB field existed
          },
        });
        logger.debug(
          {
            orderId: order.id,
            forceRecreate: options.forceRecreate,
            preserveText: options.preserveText,
            error: errorMsg,
          },
          `[AI][Order ${order.id}] Failed AI call logged to database with processing flags.`
        );
      } catch (logError) {
        logger.error(
          `[AI][Order ${order.id}] Failed to log AI error to database: ${logError instanceof Error ? logError.message : String(logError)}`
        );
      }
    }

    return {
      success: false,
      error: errorMsg,
      itemsSentToAi: itemsForPrompt,
      promptUsed: fullPromptForDebug,
      rawResponse,
      modelUsed,
      liveCustomerNotes,
      liveInternalNotes,
    };
  }
}

// --- Database Task Creation Logic ---
// Functions moved to ./database/task-operations.js

// --- Main Execution ---
async function main() {
  const SCRIPT_NAME = 'populate-print-queue';
  let scriptRunSuccess = true;
  let finalMessage = 'Script finished.';
  let mainTryCatchError: Error | null = null;
  let isPrismaConnected = false;
  let fileLogStream: fsCallback.WriteStream | null = null;
  let shuttingDown = false;

  // Counters
  let totalOrdersProcessed = 0;
  let totalOrdersFailed = 0;
  let totalTasksCreated = 0;
  const failedOrderIds: number[] = [];

  // STEP 1: Full Logger Initialization (re-assigns module-scoped logger)
  const logDir = path.join(process.cwd(), SCRIPT_LOG_DIR);
  const logFilePath = path.join(
    logDir,
    `${SCRIPT_NAME}-${new Date().toISOString().replace(/[:.]/g, '-')}.log`
  );
  try {
    await fs.mkdir(logDir, { recursive: true });
    fileLogStream = fsCallback.createWriteStream(logFilePath, { flags: 'a' });
    logger = pino(
      // Re-assigns the module-scoped logger
      { level: 'info' }, // Default level
      // eslint-disable-next-line import/no-named-as-default-member
      pino.multistream([{ stream: process.stdout }, { stream: fileLogStream }])
    );
  } catch (logSetupError: unknown) {
    const setupErr =
      logSetupError instanceof Error ? logSetupError : new Error(String(logSetupError));
    (logger || console).error(
      { err: setupErr, stack: setupErr.stack },
      'Critical error setting up logger!'
    );
    if (!fileLogStream) logger = pino({ level: 'info' }); // Fallback to stdout if file stream failed
  }

  // STEP 2: Define cleanupAndExit (uses module-scoped logger)
  async function cleanupAndExit(errorForCleanup: Error | null, triggerEvent: string) {
    if (shuttingDown) {
      /* ... */ return;
    }
    shuttingDown = true;
    const isErrorCondition = !!errorForCleanup || !scriptRunSuccess;
    const finalExitCode = isErrorCondition ? 1 : 0;
    logger.info(
      { triggerEvent, error: errorForCleanup?.message, finalExitCode },
      `Shutdown sequence started.`
    );
    if (isPrismaConnected) {
      /* ... disconnect prisma ... */
    }
    if (typeof logger.flush === 'function') {
      /* ... flush logger ... */
    }
    if (fileLogStream && !fileLogStream.destroyed) {
      /* ... close fileLogStream ... */
    }
    await new Promise(resolve => setTimeout(resolve, 100));
    process.exit(finalExitCode);
  }

  // STEP 3: Attach Signal Handlers (call cleanupAndExit)
  process.on('uncaughtException', _errCaught => {
    // Prefixed with _
    // Use console.error as logger itself might be compromised or in an unstable state
    (logger || console).fatal({ err: _errCaught, stack: _errCaught.stack }, 'UNCAUGHT EXCEPTION!');
    mainTryCatchError = _errCaught;
    scriptRunSuccess = false;
    cleanupAndExit(_errCaught, 'uncaughtException');
  });
  process.on('unhandledRejection', _reason => {
    // Prefixed with _
    const err = _reason instanceof Error ? _reason : new Error(String(_reason));
    (logger || console).fatal({ err, stack: err.stack }, 'UNHANDLED REJECTION!');
    mainTryCatchError = err;
    scriptRunSuccess = false;
    cleanupAndExit(err, 'unhandledRejection');
  });
  ['SIGINT', 'SIGTERM', 'SIGQUIT'].forEach(signal =>
    process.on(signal, () => {
      (logger || console).warn(`Received ${signal}. Shutting down gracefully...`);
      scriptRunSuccess = false; // Signal means not a full success
      // Pass a new error object for signals, as there isn't an existing error object
      cleanupAndExit(new Error(`Received signal: ${signal} `), signal);
    })
  );

  // STEP 4: Main Try-Catch-Finally Block
  try {
    // STEP 4a: Commander Setup & Parse (AFTER logger is fully init)
    const program = new Command();
    program
      .name(SCRIPT_NAME)
      .description('Fetch orders and create print tasks via AI.')
      .option(
        '-o, --order-id <id>',
        'Process order by DB ID, ShipStation Order Number, or ShipStation Order ID',
        String
      )
      .option('-l, --limit <number>', 'Limit orders fetched', val => parseInt(val, 10))
      .option('--days <number>', 'Limit orders to those created in the last X days', val =>
        parseInt(val, 10)
      )
      .option('--openai-api-key <key>', 'OpenAI API Key', process.env.OPENAI_API_KEY)
      .option('--openai-model <model>', 'OpenAI model', DEFAULT_OPENAI_MODEL)
      .option(
        '--system-prompt-file <path>',
        'Path to system prompt file',
        DEFAULT_SYSTEM_PROMPT_PATH
      )
      .option(
        '--user-prompt-file <path>',
        'Path to user prompt template file',
        DEFAULT_USER_PROMPT_PATH
      )
      .option('--debug', 'Enable debug logging', false)
      .option('--verbose', 'Enable verbose logging', false)
      .option('--log-level <level>', 'Set log level', 'info')
      .option('-f, --force-recreate', 'Delete existing tasks first', false)
      .option('--create-placeholder', 'Create placeholder on AI fail or if AI is skipped', true)
      .option('-y, --confirm', 'Skip confirmation prompts', false)
      .option('--clear-all', 'Delete ALL tasks first (requires confirm)', false)
      .option('--dry-run', 'Simulate without DB changes', false)
      .option('--preserve-text', 'Keep existing custom text/names when recreating tasks', false)
      .option('--skip-ai', 'Skip AI extraction step', false)
      .option(
        '--sync-to-shipstation',
        'Synchronize with ShipStation (on by default). Use --no-sync-to-shipstation to disable.',
        true
      )
      .option(
        '--shipstation-sync-only',
        'Only sync existing DB tasks to ShipStation without changing DB',
        false
      )
      .option(
        '--debug-file <path>',
        'Path for detailed debug log file (requires --order-id)',
        String
      );
    program.parse(process.argv);
    const cmdOptions = program.opts<ProcessingOptions>();

    if (cmdOptions.verbose) logger.level = 'debug';
    else if (cmdOptions.logLevel) logger.level = cmdOptions.logLevel;

    logger.info(`-- - Script Start: ${new Date().toISOString()} --- `);
    logger.info(`Logging to file: ${logFilePath} `);
    logger.info(`Effective logger level: ${logger.level} `);
    logger.info(`Parsed CLI Options: ${JSON.stringify({ ...cmdOptions, openaiApiKey: '***' })} `);

    cmdOptions.systemPrompt = await loadPromptFile(
      cmdOptions.systemPromptFile || DEFAULT_SYSTEM_PROMPT_PATH
    );
    cmdOptions.userPromptTemplate = await loadPromptFile(
      cmdOptions.userPromptFile || DEFAULT_USER_PROMPT_PATH
    );
    logger.info(`Prompts loaded.`);

    if (!cmdOptions.openaiApiKey) throw new Error('OpenAI API key missing.');

    await prisma.$connect();
    isPrismaConnected = true;
    logger.info('DB connected.');

    await createRunLog({ scriptName: SCRIPT_NAME });
    await fixInvalidStlRenderStatus(prisma);

    logger.info('Finding orders...');
    const ordersToProcess = await getOrdersToProcess(
      prisma,
      cmdOptions.orderId,
      cmdOptions.limit,
      cmdOptions.forceRecreate
    );
    logger.info({ count: ordersToProcess.length }, `Orders to process found.`);

    for (const order of ordersToProcess) {
      totalOrdersProcessed++;
      logger.info(
        {
          orderId: order.id,
          orderNumber: order.shipstation_order_number,
          marketplace: order.marketplace,
        },
        `-- - Processing Order ${order.id} (Marketplace: ${order.marketplace}) --- `
      );

      let orderProcessingSuccess = true; // Flag for this specific order
      // Initialize variables that will hold results from AI extraction or defaults
      let itemsSentToAiForTransaction: AiOrderItemData[] = [];
      let aiDataForTransaction: z.infer<typeof AiOrderResponseSchema> = {
        itemPersonalizations: {},
      };

      if (cmdOptions.shipstationSyncOnly) {
        logger.info(
          { orderId: order.id },
          `ShipStation sync - only mode enabled.Skipping AI extraction and DB updates.`
        );
        try {
          const { updatedCount, failedCount } = await syncExistingTasksToShipstation(
            prisma,
            order.id,
            cmdOptions,
            logger
          );
          logger.info(
            { orderId: order.id, updatedCount, failedCount },
            `ShipStation sync completed for order.`
          );
        } catch (syncErr: unknown) {
          const actualSyncError = syncErr instanceof Error ? syncErr : new Error(String(syncErr));
          logger.error(
            { orderId: order.id, err: actualSyncError, stack: actualSyncError.stack },
            `ShipStation sync - only failed for order.`
          );
          totalOrdersFailed++;
          failedOrderIds.push(order.id);
          orderProcessingSuccess = false;
        }
        continue; // Move to the next order
      }

      const orderDebugInfo: OrderDebugInfo = {
        orderId: order.id,
        orderNumber: order.shipstation_order_number ?? '',
        marketplace: order.marketplace,
        overallStatus: 'Starting',
        promptSent: null,
        rawResponseReceived: null,
        parsedResponse: null,
        processingError: null,
        aiProvider: 'openai',
        modelUsed: cmdOptions.openaiModel,
        items: [],
        forceRecreate: cmdOptions.forceRecreate,
        preserveText: cmdOptions.preserveText,
        skipAi: cmdOptions.skipAi,
      };

      try {
        await appendToDebugLog(cmdOptions.debugFile, orderDebugInfo);
        orderDebugInfo.overallStatus = 'Extracting AI Data';

        let liveCustomerNotesForTransaction: string | null | undefined = null;
        let liveInternalNotesForTransaction: string | null | undefined = null;

        // Fetch live notes directly if force-recreate is set
        if (
          cmdOptions.forceRecreate &&
          order.shipstation_order_id &&
          process.env.SHIPSTATION_API_KEY &&
          process.env.SHIPSTATION_API_SECRET
        ) {
          try {
            logger.info(
              { orderId: order.id, shipstationOrderId: order.shipstation_order_id },
              '[LiveNotes] Directly fetching live notes from ShipStation due to --force-recreate...'
            );
            const liveNotesResult = await fetchShipStationOrderDetails(
              process.env.SHIPSTATION_API_KEY,
              process.env.SHIPSTATION_API_SECRET,
              order.shipstation_order_id
            );
            if (liveNotesResult) {
              liveCustomerNotesForTransaction = liveNotesResult.customerNotes;
              liveInternalNotesForTransaction = liveNotesResult.internalNotes;
              logger.info(
                {
                  orderId: order.id,
                  shipstationOrderId: order.shipstation_order_id,
                  liveCustomerNotes: liveCustomerNotesForTransaction,
                  liveInternalNotes: liveInternalNotesForTransaction,
                },
                '[LiveNotes] Successfully fetched live notes directly from ShipStation.'
              );
            } else {
              logger.warn(
                { orderId: order.id, shipstationOrderId: order.shipstation_order_id },
                '[LiveNotes] Failed to fetch live notes directly from ShipStation (API returned null).'
              );
            }
          } catch (err) {
            logger.error(
              { orderId: order.id, shipstationOrderId: order.shipstation_order_id, err },
              '[LiveNotes] Error directly fetching live notes from ShipStation.'
            );
          }
        }

        if (cmdOptions.skipAi) {
          logger.info({ orderId: order.id }, 'Skipping AI extraction as per --skip-ai flag.');
          orderDebugInfo.overallStatus = 'AI Skipped';
          // itemsSentToAiForTransaction remains []
          // aiDataForTransaction remains { itemPersonalizations: {} }
        } else {
          const extractionResult = await extractOrderPersonalization(order, {
            openaiApiKey: cmdOptions.openaiApiKey,
            openaiModel: cmdOptions.openaiModel,
            systemPrompt: cmdOptions.systemPrompt,
            userPromptTemplate: cmdOptions.userPromptTemplate,
            forceRecreate: cmdOptions.forceRecreate,
            preserveText: cmdOptions.preserveText,
            dryRun: cmdOptions.dryRun,
          });
          itemsSentToAiForTransaction = extractionResult.itemsSentToAi; // Populate here
          orderDebugInfo.promptSent = extractionResult.promptUsed;
          orderDebugInfo.rawResponseReceived = extractionResult.rawResponse;
          orderDebugInfo.modelUsed = extractionResult.modelUsed ?? cmdOptions.openaiModel;

          if (!extractionResult.success || !extractionResult.aiResponseData) {
            const aiErrorMsg = extractionResult.error || 'AI extraction returned no data';
            orderDebugInfo.processingError = aiErrorMsg;
            logger.error({ orderId: order.id, err: aiErrorMsg }, 'AI Extraction Failed');
            orderDebugInfo.overallStatus = 'AI Extraction Failed';
            if (cmdOptions.createPlaceholder) {
              logger.info(
                { orderId: order.id },
                'Proceeding with placeholder creation due to AI failure.'
              );
              // aiDataForTransaction remains { itemPersonalizations: {} }, placeholder logic is in createOrUpdateTasksInTransaction
            } else {
              throw new Error(aiErrorMsg); // This will be caught by the outer catch for this order
            }
          } else {
            aiDataForTransaction = extractionResult.aiResponseData; // Assign successful AI response
            liveCustomerNotesForTransaction = extractionResult.liveCustomerNotes;
            liveInternalNotesForTransaction = extractionResult.liveInternalNotes;
            orderDebugInfo.parsedResponse = aiDataForTransaction;
            orderDebugInfo.overallStatus = 'AI Data Extracted';
          }
        }
        await appendToDebugLog(cmdOptions.debugFile, orderDebugInfo);

        if (cmdOptions.dryRun) {
          logger.info(
            { orderId: order.id },
            '[Dry Run] Simulating task creation/DB upserts and ShipStation sync...'
          );
          orderDebugInfo.overallStatus = 'Dry Run Simulation';

          // Simulate the data sourcing logic that createOrUpdateTasksInTransaction would perform
          const itemsToPatchForSsDryRun: Record<
            string,
            Array<{ name: string; value: string | null }>
          > = {};
          const patchReasonsSsDryRun: string[] = [];

          for (const item of order.OrderItem) {
            const product = item.Product; // Ensure product is available
            const lineItemKey = item.shipstationLineItemKey;

            logger.info(
              { orderId: order.id, itemId: item.id, lineItemKey },
              '[Dry Run] Simulating data extraction for item.'
            );
            const directExtractionResult = await extractDirectItemData(order, item, product);

            let taskDataSource = 'Unknown';
            let customTextForLog: string | null | undefined = null;
            let color1ForLog: string | null | undefined = null;
            let color2ForLog: string | null | undefined = null;
            let needsReviewForLog = false;
            let reviewReasonForLog: string | null | undefined = null;

            if (directExtractionResult.dataSource === 'AmazonURL') {
              taskDataSource = 'AmazonURL';
              customTextForLog = directExtractionResult.customText;
              color1ForLog = directExtractionResult.color1;
              color2ForLog = directExtractionResult.color2;
              needsReviewForLog = directExtractionResult.needsReview || false;
              reviewReasonForLog = directExtractionResult.reviewReason;
              logger.info(
                {
                  orderId: order.id,
                  itemId: item.id,
                  source: taskDataSource,
                  data: directExtractionResult,
                },
                '[Dry Run] Would use data from Amazon URL.'
              );
              if (lineItemKey && (customTextForLog || color1ForLog || color2ForLog)) {
                const ssOptions = [];
                if (customTextForLog)
                  ssOptions.push({ name: 'Name or Text', value: customTextForLog });
                if (color1ForLog) ssOptions.push({ name: 'Colour 1', value: color1ForLog });
                if (color2ForLog) ssOptions.push({ name: 'Colour 2', value: color2ForLog });
                if (ssOptions.length > 0) itemsToPatchForSsDryRun[lineItemKey] = ssOptions;
                patchReasonsSsDryRun.push(`${lineItemKey}(${taskDataSource})`);
              }
            } else {
              taskDataSource = 'AI/Placeholder';
              const aiItemData = lineItemKey
                ? aiDataForTransaction.itemPersonalizations[lineItemKey]
                : null;
              if (aiItemData && aiItemData.personalizations.length > 0) {
                const firstPz = aiItemData.personalizations[0]; // Assuming single personalization for dry run log simplicity
                customTextForLog = firstPz.customText;
                color1ForLog = firstPz.color1;
                color2ForLog = firstPz.color2;
                needsReviewForLog = firstPz.needsReview || aiItemData.overallNeedsReview;
                reviewReasonForLog = firstPz.reviewReason || aiItemData.overallReviewReason;
                logger.info(
                  { orderId: order.id, itemId: item.id, source: 'AI', data: firstPz },
                  '[Dry Run] Would use data from AI.'
                );

                // ShipStation Personalized Details string for AI data - conditional
                const hasMeaningfulAiDryRun = aiItemData.personalizations?.some(
                  p => p.customText || p.color1 || p.color2
                );

                if (lineItemKey && hasMeaningfulAiDryRun) {
                  const detailsString = buildPersonalizedDetailsString(
                    aiItemData.personalizations || [],
                    lineItemKey,
                    order.id
                  );
                  itemsToPatchForSsDryRun[lineItemKey] = [
                    { name: 'Personalized Details', value: detailsString },
                  ];
                  patchReasonsSsDryRun.push(`${lineItemKey}(AI-PD)`);
                  logger.info(
                    { orderId: order.id, itemId: item.id, lineItemKey },
                    '[Dry Run] Prepared ShipStation sync for AI data (meaningful data found).'
                  );
                } else if (lineItemKey) {
                  logger.info(
                    { orderId: order.id, itemId: item.id, lineItemKey },
                    '[Dry Run] Skipping ShipStation sync for AI data (AI data not meaningful).'
                  );
                }
              } else if (cmdOptions.createPlaceholder) {
                taskDataSource = 'Placeholder';
                customTextForLog = 'Placeholder - Check Order Details';
                needsReviewForLog = true;
                reviewReasonForLog = `No AI/direct data; placeholder would be created.`;
                logger.info(
                  { orderId: order.id, itemId: item.id, source: taskDataSource },
                  '[Dry Run] Would create placeholder task.'
                );
              } else {
                taskDataSource = 'Skipped_No_Data';
                logger.info(
                  { orderId: order.id, itemId: item.id, source: taskDataSource },
                  '[Dry Run] Would skip task creation (no data, no placeholder).'
                );
              }
            }
            // Log what would be created for this item (simplified)
            logger.info(
              {
                orderId: order.id,
                itemId: item.id,
                lineItemKey,
                taskDataSource,
                customText: customTextForLog,
                color1: color1ForLog,
                color2: color2ForLog,
                needsReview: needsReviewForLog,
                reviewReason: reviewReasonForLog,
              },
              '[Dry Run] Simulated task detail for item.'
            );
          }

          // Log overall AI data that was received (already present, but good for context)
          // const itemsToLogForDryRun = itemsSentToAiForTransaction; // This was used before, keep for now if needed for other logs
          // const aiDataToLogForDryRun = aiDataForTransaction;
          // logger.info({ orderId: order.id, itemsCount: itemsToLogForDryRun.length, hasAiData: !!aiDataToLogForDryRun }, '[Dry Run] Original AI prompt items & response summary.');
          if (
            itemsSentToAiForTransaction.some((item: AiOrderItemData) => item._amazonDataProcessed)
          ) {
            logger.info(
              { orderId: order.id },
              '[Dry Run] Note: itemsSentToAiForTransaction indicates some items had _amazonDataProcessed flag set during AI prep.'
            );
          }
          if (
            aiDataForTransaction &&
            Object.keys(aiDataForTransaction.itemPersonalizations).length > 0
          ) {
            logger.info(
              { orderId: order.id, aiData: aiDataForTransaction },
              '[Dry Run] Full AI response that was received.'
            );
          }

          // Simulate ShipStation sync logging more accurately
          if (cmdOptions.syncToShipstation && order.shipstation_order_id) {
            if (Object.keys(itemsToPatchForSsDryRun).length > 0) {
              // Reconstruct a simplified packing list for the dry run log based on simulated data
              // const packingListLinesDryRun: string[] = []; // Removed as unused
              // This part is tricky as it depends on the *actual* tasks that would be created.
              // For simplicity, we'll just indicate that notes would be updated.
              // A more complex simulation would build this from the customTextForLog etc. per item.
              logger.info(
                {
                  orderId: order.id,
                  itemsToPatch: itemsToPatchForSsDryRun,
                  patchReasons: patchReasonsSsDryRun,
                  auditNotePreview: `PACKING LIST(...) Original Customer Notes(...) Automated Task Sync(Dry Run) ${new Date().toISOString()} -> ${patchReasonsSsDryRun.join(', ')}`,
                },
                '[Dry Run] Would update ShipStation with item options and internalNotes.'
              );
            } else {
              logger.info(
                { orderId: order.id },
                '[Dry Run] No item options identified for ShipStation patching during simulation.'
              );
            }
          }
          orderDebugInfo.overallStatus = 'Dry Run Simulation Complete';
        } else {
          orderDebugInfo.overallStatus = 'Starting DB Transaction';
          // aiDataForTransaction is already populated from the block above
          const { tasksCreatedCount: currentTasks } = await prisma.$transaction(
            async tx => {
              const itemsToPassToTransaction = itemsSentToAiForTransaction; // Use the correctly populated variable
              // aiDataForTransaction holds AI response or default, so it's fine to pass directly
              return createOrUpdateTasksInTransaction(
                tx,
                order,
                aiDataForTransaction,
                cmdOptions,
                orderDebugInfo,
                itemsToPassToTransaction,
                liveCustomerNotesForTransaction,
                liveInternalNotesForTransaction,
                logger
              );
            },
            {
              maxWait: PRISMA_TRANSACTION_MAX_WAIT,
              timeout: PRISMA_TRANSACTION_TIMEOUT,
            }
          );
          totalTasksCreated += currentTasks;
          logger.info(
            { orderId: order.id, tasksCreated: currentTasks },
            'DB Transaction finished.'
          );
          if (!orderDebugInfo.processingError)
            orderDebugInfo.overallStatus = 'Transaction Committed';
        }
      } catch (orderProcessingError: unknown) {
        // Typed as unknown
        orderProcessingSuccess = false;
        const actualError =
          orderProcessingError instanceof Error
            ? orderProcessingError
            : new Error(String(orderProcessingError));
        logger.error(
          { orderId: order.id, err: actualError, stack: actualError.stack },
          `Error processing order ${order.id}.`
        );
        orderDebugInfo.processingError = actualError.message;
        orderDebugInfo.overallStatus = 'Processing Failed';
        // Placeholder creation logic if AI failed or this block is hit, was more complex before, ensure it's correct
        if (cmdOptions.createPlaceholder) {
          logger.info(
            { orderId: order.id },
            'Creating placeholder task as order processing failed and createPlaceholder is true.'
          );
          // This part might need access to `taskDetailsToCreate` and `finalDataSource` if they were defined in this scope
          // For now, just logging. If tasks need to be created here, that logic needs to be in scope.
        }
      } finally {
        await appendToDebugLog(cmdOptions.debugFile, orderDebugInfo);
        logger.info(
          { orderId: order.id, finalItemStatus: orderDebugInfo.overallStatus },
          `-- - Finished Order ${order.id} --- `
        );
        if (!orderProcessingSuccess) {
          totalOrdersFailed++;
          failedOrderIds.push(order.id);
        }
      }
    } // End of for...of loop for orders

    finalMessage = `Processed ${totalOrdersProcessed} orders.Succeeded: ${totalOrdersProcessed - totalOrdersFailed}.Failed: ${totalOrdersFailed}. Tasks Upserted: ${totalTasksCreated}.`;
    if (totalOrdersFailed > 0) finalMessage += ` Failed Order IDs: [${failedOrderIds.join(', ')}]`;
    scriptRunSuccess = totalOrdersFailed === 0;
    if (runLogId)
      await updateRunLog(runLogId, { status: scriptRunSuccess ? 'success' : 'partial_success' });
  } catch (error: unknown) {
    mainTryCatchError = error instanceof Error ? error : new Error(String(error));
    (logger || console).error(
      { err: mainTryCatchError, stack: mainTryCatchError.stack },
      `SCRIPT FAILED: ${mainTryCatchError.message} `
    );
    scriptRunSuccess = false;
    finalMessage = `Script FAILED: ${mainTryCatchError.message} `;
    if (runLogId)
      try {
        await updateRunLog(runLogId, { status: 'failed', message: mainTryCatchError.message });
      } catch (e) {
        (logger || console).error('Failed to update run log on error', e);
      }
  } finally {
    (logger || console).info(`-- - Script End-- - `);
    (logger || console).info(finalMessage);
    await cleanupAndExit(
      mainTryCatchError,
      mainTryCatchError ? 'errorInMainFinally' : 'normalCompletionFinally'
    );
  }
}

void main().catch(err => {
  (logger || console).error('Catastrophic error in main function execution (outer catch):', err);
  if (isPrismaConnected) {
    prisma.$disconnect().catch(e => console.error('Catastrophic disconnect error', e));
  }
  process.exit(1);
});
